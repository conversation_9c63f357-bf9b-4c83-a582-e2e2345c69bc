/**
 * Redis integration for WhatsApp Agent
 * 
 * Handles Redis connection and provides functions for accessing real-time doctor availability
 */

import { createClient } from 'redis';
import { logger } from './logger.js';

// Redis client instance
let redisClient = null;

/**
 * Initialize Redis connection
 */
export const initializeRedis = async () => {
  try {
    // Check if already initialized and connected
    if (redisClient && redisClient.isReady) {
      logger.info('Redis already connected');
      return redisClient;
    }

    // Create new Redis client
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    redisClient = createClient({ url: redisUrl });

    // Set up event handlers
    redisClient.on('error', (err) => {
      logger.error('Redis error:', err);
    });

    redisClient.on('connect', () => {
      logger.info('Redis connected');
    });

    redisClient.on('reconnecting', () => {
      logger.info('Redis reconnecting');
    });

    redisClient.on('ready', () => {
      logger.info('Redis ready');
    });

    // Connect to Redis
    await redisClient.connect();
    
    return redisClient;
  } catch (error) {
    logger.error('Redis initialization error:', error);
    throw error;
  }
};

/**
 * Get Redis client instance
 */
export const getRedisClient = () => {
  if (!redisClient || !redisClient.isReady) {
    throw new Error('Redis not initialized or not connected. Call initializeRedis() first.');
  }
  return redisClient;
};

/**
 * Get doctor arrival status from Redis
 * @param {string} hospitalId - Hospital ID
 * @param {string} doctorId - Doctor ID
 * @returns {Promise<string>} - Arrival status (e.g., 'arrived', '15 minutes')
 */
export const getDoctorArrivalStatus = async (hospitalId, doctorId) => {
  try {
    const client = getRedisClient();
    const key = `hospital:${hospitalId}:doctor:${doctorId}:arrival_status`;
    const status = await client.get(key);
    
    if (!status) {
      logger.debug(`No arrival status found for doctor ${doctorId} at hospital ${hospitalId}`);
      return null;
    }
    
    return status;
  } catch (error) {
    logger.error(`Error getting arrival status for doctor ${doctorId}:`, error);
    throw error;
  }
};

/**
 * Get doctor arrival time from Redis
 * @param {string} hospitalId - Hospital ID
 * @param {string} doctorId - Doctor ID
 * @returns {Promise<string>} - Arrival time (ISO string)
 */
export const getDoctorArrivalTime = async (hospitalId, doctorId) => {
  try {
    const client = getRedisClient();
    const key = `hospital:${hospitalId}:doctor:${doctorId}:arrival_time`;
    const time = await client.get(key);
    
    if (!time) {
      logger.debug(`No arrival time found for doctor ${doctorId} at hospital ${hospitalId}`);
      return null;
    }
    
    return time;
  } catch (error) {
    logger.error(`Error getting arrival time for doctor ${doctorId}:`, error);
    throw error;
  }
};

/**
 * Format doctor availability message based on Redis data
 * @param {string} hospitalId - Hospital ID
 * @param {string} doctorId - Doctor ID
 * @param {string} doctorName - Doctor name
 * @returns {Promise<string>} - Formatted availability message
 */
export const formatDoctorAvailabilityMessage = async (hospitalId, doctorId, doctorName) => {
  try {
    const status = await getDoctorArrivalStatus(hospitalId, doctorId);
    
    if (!status) {
      return `We don't have real-time information about Dr. ${doctorName}'s arrival at the moment.`;
    }
    
    if (status.toLowerCase() === 'arrived') {
      return `Dr. ${doctorName} has arrived at the hospital.`;
    }
    
    // Check if status is a time value (e.g., "15 minutes")
    if (status.includes('minute') || status.includes('hour')) {
      return `Dr. ${doctorName} will arrive in ${status}.`;
    }
    
    // Default message for other status values
    return `Dr. ${doctorName}'s status: ${status}`;
  } catch (error) {
    logger.error(`Error formatting availability message for doctor ${doctorId}:`, error);
    return `We're unable to retrieve Dr. ${doctorName}'s availability information at the moment.`;
  }
};

export default {
  initializeRedis,
  getRedisClient,
  getDoctorArrivalStatus,
  getDoctorArrivalTime,
  formatDoctorAvailabilityMessage
};
