"""
Configuration file data loader for voice agent system.
Loads hospital data from JSON configuration files as fallback.
"""

import json
import logging
import os
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


class ConfigDataLoader:
    """
    Configuration file data loader for hospital information.
    Provides fallback when Firebase data is not available.
    """

    def __init__(self, config_dir: str = None):
        """
        Initialize configuration data loader.
        
        Args:
            config_dir: Directory containing configuration files
        """
        if config_dir is None:
            # Default to config directory relative to this file
            current_dir = os.path.dirname(os.path.abspath(__file__))
            self.config_dir = os.path.join(os.path.dirname(current_dir), 'config')
        else:
            self.config_dir = config_dir
        
        self.hospital_templates_dir = os.path.join(self.config_dir, 'hospital_templates')
        self.languages_dir = os.path.join(self.config_dir, 'languages')
        
        # Ensure directories exist
        self._ensure_directories()

    def _ensure_directories(self):
        """Ensure configuration directories exist."""
        try:
            os.makedirs(self.hospital_templates_dir, exist_ok=True)
            os.makedirs(self.languages_dir, exist_ok=True)
            logger.info(f"Configuration directories ensured: {self.config_dir}")
        except Exception as e:
            logger.error(f"Error creating configuration directories: {e}")

    def load_hospital_data(self, hospital_id: str) -> Dict[str, Any]:
        """
        Load hospital data from configuration files.

        Args:
            hospital_id: Hospital identifier

        Returns:
            Dict containing doctors, tests, and hospital info
        """
        try:
            # Try hospital-specific config first
            hospital_config = self._load_hospital_config(hospital_id)
            
            if not hospital_config:
                # Fall back to default template
                hospital_config = self._load_default_template()
                if hospital_config:
                    hospital_config['hospital_id'] = hospital_id
                    logger.info(f"Using default template for hospital {hospital_id}")

            if not hospital_config:
                logger.warning(f"No configuration found for hospital {hospital_id}")
                return self._get_empty_hospital_data(hospital_id)

            # Ensure required structure
            hospital_data = {
                "hospital_id": hospital_id,
                "doctors": hospital_config.get("doctors", []),
                "tests": hospital_config.get("tests", []),
                "hospital_info": hospital_config.get("hospital_info", {}),
                "languages": hospital_config.get("languages", ["hi", "bn", "en"])
            }

            logger.info(f"Loaded config data for {hospital_id}: "
                       f"{len(hospital_data['doctors'])} doctors, "
                       f"{len(hospital_data['tests'])} tests")

            return hospital_data

        except Exception as e:
            logger.error(f"Error loading config data for {hospital_id}: {e}")
            return self._get_empty_hospital_data(hospital_id)

    def _load_hospital_config(self, hospital_id: str) -> Optional[Dict[str, Any]]:
        """Load hospital-specific configuration file."""
        config_file = os.path.join(self.hospital_templates_dir, f'hospital_{hospital_id}.json')
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"Loaded hospital-specific config: {config_file}")
                return config
            except Exception as e:
                logger.error(f"Error loading hospital config {config_file}: {e}")
        
        return None

    def _load_default_template(self) -> Optional[Dict[str, Any]]:
        """Load default hospital template."""
        template_file = os.path.join(self.hospital_templates_dir, 'default.json')
        
        if os.path.exists(template_file):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    template = json.load(f)
                logger.info(f"Loaded default template: {template_file}")
                return template
            except Exception as e:
                logger.error(f"Error loading default template {template_file}: {e}")
        
        return None

    def _get_empty_hospital_data(self, hospital_id: str) -> Dict[str, Any]:
        """Get empty hospital data structure."""
        return {
            "hospital_id": hospital_id,
            "doctors": [],
            "tests": [],
            "hospital_info": {
                "id": hospital_id,
                "name": f"Hospital {hospital_id}",
                "languages": ["hi", "bn", "en"]
            },
            "languages": ["hi", "bn", "en"]
        }

    def load_query_patterns(self) -> Dict[str, Any]:
        """Load query generation patterns."""
        patterns_file = os.path.join(self.hospital_templates_dir, 'query_patterns.json')
        
        if os.path.exists(patterns_file):
            try:
                with open(patterns_file, 'r', encoding='utf-8') as f:
                    patterns = json.load(f)
                logger.info(f"Loaded query patterns: {patterns_file}")
                return patterns
            except Exception as e:
                logger.error(f"Error loading query patterns {patterns_file}: {e}")
        
        # Return default patterns if file doesn't exist
        return self._get_default_query_patterns()

    def _get_default_query_patterns(self) -> Dict[str, Any]:
        """Get default query patterns."""
        return {
            "doctor_patterns": {
                "name_query": [
                    "when does {doctor_name} come",
                    "what time does {doctor_name} arrive",
                    "is {doctor_name} available"
                ],
                "specialty_query": [
                    "when is the {specialty} available",
                    "what time does the {specialty} come",
                    "is the {specialty} doctor available"
                ]
            },
            "test_patterns": {
                "name_query": [
                    "what is the price of {test_name}",
                    "how much does {test_name} cost",
                    "what is the fee for {test_name}"
                ],
                "general_query": [
                    "blood test price",
                    "test costs",
                    "examination fees"
                ]
            }
        }

    def load_language_templates(self, language: str) -> Dict[str, Any]:
        """
        Load language-specific query templates.
        
        Args:
            language: Language code (hi, bn, en, etc.)
            
        Returns:
            Dict containing language-specific templates
        """
        template_file = os.path.join(self.languages_dir, f'query_templates_{language}.json')
        
        if os.path.exists(template_file):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    templates = json.load(f)
                logger.info(f"Loaded language templates for {language}: {template_file}")
                return templates
            except Exception as e:
                logger.error(f"Error loading language templates {template_file}: {e}")
        
        # Return empty templates if file doesn't exist
        logger.warning(f"No language templates found for {language}")
        return {}

    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages from configuration files."""
        try:
            languages = []
            for filename in os.listdir(self.languages_dir):
                if filename.startswith('query_templates_') and filename.endswith('.json'):
                    # Extract language code from filename
                    lang_code = filename.replace('query_templates_', '').replace('.json', '')
                    languages.append(lang_code)
            
            if not languages:
                # Default languages if no config files found
                languages = ["hi", "bn", "en"]
            
            logger.info(f"Supported languages from config: {languages}")
            return languages
            
        except Exception as e:
            logger.error(f"Error getting supported languages: {e}")
            return ["hi", "bn", "en"]  # Default fallback
