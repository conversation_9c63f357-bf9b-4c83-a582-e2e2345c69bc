# Quick Start Guide - Dynamic Data Loading System

## 🚀 5-Minute Setup

### Step 1: Verify Installation
The system is already integrated. Check if files exist:
```bash
ls voice_agent/data_loaders/
ls voice_agent/config/
```

### Step 2: Test Basic Functionality
```python
# Test in Python console
from voice_agent.data_loaders import CachePreloader

preloader = CachePreloader()
print("✅ Data loaders imported successfully")
```

### Step 3: Load Your First Hospital
```python
# For existing hospital in Firebase
success = await preloader.preload_hospital_data_async("456")
print(f"Hospital 456 loaded: {success}")

# Check what was cached
stats = preloader.get_cache_statistics("456")
print(f"Cached queries: {stats['total_cached_queries']}")
```

### Step 4: Test Semantic Search
```python
from voice_agent.cache_manager import redis_manager

# Search in Hindi
results = await redis_manager.semantic_search_async(
    "डॉक्टर राजेश कब आते हैं", "456", "doctors"
)
print(f"Found {len(results)} results")

# Search in English  
results = await redis_manager.semantic_search_async(
    "when does dr raj<PERSON> come", "456", "doctors"
)
print(f"Found {len(results)} results")
```

## 🔧 Common Use Cases

### Use Case 1: Add New Hospital (Firebase)
1. Add hospital data to Firebase:
   ```
   hospitals/789/
     name: "New Hospital"
     languages: ["hi", "en"]
   
   hospital_789_data/doctors/doctors/
     doc1: {name: "Dr. Smith", specialty: "Cardiology"}
   ```

2. Preload the hospital:
   ```python
   success = await preloader.preload_hospital_data_async("789")
   ```

### Use Case 2: Add New Hospital (Configuration)
1. Create `voice_agent/config/hospital_templates/hospital_789.json`:
   ```json
   {
     "hospital_info": {"id": "789", "name": "New Hospital"},
     "doctors": [{"name": "Dr. Smith", "specialty": "Cardiology"}],
     "tests": [{"name": "Blood Test", "price": "₹500"}]
   }
   ```

2. Preload the hospital:
   ```python
   success = await preloader.preload_hospital_data_async("789")
   ```

### Use Case 3: Add New Language
1. Create `voice_agent/config/languages/query_templates_ta.json`:
   ```json
   {
     "language": "ta",
     "doctor_name_queries": ["டாக்டர் {doctor_name} எப்போது வருவார்"]
   }
   ```

2. Update hospital config to include "ta" in languages array

3. Reload hospital data:
   ```python
   await preloader.clear_hospital_cache("456")
   await preloader.preload_hospital_data_async("456")
   ```

## 🐛 Quick Troubleshooting

### Problem: "No data found for hospital"
**Solution**: Check if hospital exists in Firebase or create config file

### Problem: "Firebase connection failed"
**Solution**: System will automatically use config files as fallback

### Problem: "No queries generated"
**Solution**: Check language templates exist for hospital's languages

### Problem: "Import error for data_loaders"
**Solution**: Ensure all files are created and __init__.py exists

## 📊 Quick Verification

### Check System Status
```python
# Test all components
from voice_agent.data_loaders import *

# Test Firebase connection
firebase_loader = FirebaseDataLoader()
hospitals = await firebase_loader.get_all_hospitals()
print(f"Firebase hospitals: {len(hospitals)}")

# Test config loading
config_loader = ConfigDataLoader()
data = config_loader.load_hospital_data("456")
print(f"Config data loaded: {bool(data)}")

# Test query generation
query_generator = QueryGenerator()
queries = query_generator.generate_doctor_queries([
    {"name": "Dr. Test", "specialty": "Cardiology"}
])
print(f"Generated queries: {len(queries)}")
```

### Performance Check
```python
import time

start = time.time()
success = await preloader.preload_hospital_data_async("456")
duration = time.time() - start

print(f"Preload completed in {duration:.2f}s")
print(f"Success: {success}")
```

## 🎯 Next Steps

1. **Review Configuration**: Check `voice_agent/config/` files
2. **Test with Your Data**: Add your hospital configurations
3. **Monitor Performance**: Check logs and response times
4. **Scale Up**: Use `preload_all_hospitals()` for production

## 📞 Quick Help

- **Logs**: Check console output for detailed error messages
- **Redis**: Ensure Redis is running on localhost:6379
- **Firebase**: Verify GOOGLE_APPLICATION_CREDENTIALS is set
- **Permissions**: Check file permissions for config directory

---

**Ready to go!** The system is now configured to eliminate all hardcoded values and load data dynamically from Firebase with configuration file fallback.
