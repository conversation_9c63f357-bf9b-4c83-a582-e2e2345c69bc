/**
 * Hospitals configuration module for WhatsApp Agent
 * 
 * Loads hospital configurations from JSON file and provides access to hospital-specific settings
 */

import fs from 'fs/promises';
import path from 'path';
import { logger } from './logger.js';

// Store loaded hospital configurations
let hospitals = [];
let hospitalsMap = new Map();

/**
 * Load hospital configurations from JSON file
 */
export const loadHospitals = async () => {
  try {
    const filePath = path.join(process.cwd(), '..', 'data_init', 'data', 'hospitals.json');
    const data = await fs.readFile(filePath, 'utf8');
    hospitals = JSON.parse(data);
    
    // Create a map for quick access by ID
    hospitalsMap.clear();
    hospitals.forEach(hospital => {
      hospitalsMap.set(hospital.id, hospital);
    });
    
    logger.info(`Loaded ${hospitals.length} hospital configurations`);
    return hospitals;
  } catch (error) {
    logger.error('Error loading hospital configurations:', error);
    throw error;
  }
};

/**
 * Get all hospitals
 * @returns {Array} - Array of hospital configurations
 */
export const getHospitals = () => {
  return hospitals;
};

/**
 * Get hospital by ID
 * @param {string} hospitalId - Hospital ID
 * @returns {Object} - Hospital configuration
 */
export const getHospitalById = (hospitalId) => {
  return hospitalsMap.get(hospitalId);
};

/**
 * Get hospital by phone number
 * @param {string} phoneNumber - Hospital phone number
 * @returns {Object} - Hospital configuration
 */
export const getHospitalByPhone = (phoneNumber) => {
  // Normalize phone number for comparison
  const normalizedPhone = normalizePhoneNumber(phoneNumber);
  
  return hospitals.find(hospital => {
    const hospitalPhone = normalizePhoneNumber(hospital.phone);
    return hospitalPhone === normalizedPhone;
  });
};

/**
 * Get hospital database connection string
 * @param {string} hospitalId - Hospital ID
 * @returns {string} - PostgreSQL connection string
 */
export const getHospitalDbConnection = (hospitalId) => {
  const hospital = getHospitalById(hospitalId);
  if (!hospital) {
    throw new Error(`Hospital ${hospitalId} not found`);
  }
  return hospital.db_connection_string;
};

/**
 * Check if hospital has WhatsApp service enabled
 * @param {string} hospitalId - Hospital ID
 * @returns {boolean} - True if WhatsApp service is enabled
 */
export const hasWhatsAppService = (hospitalId) => {
  const hospital = getHospitalById(hospitalId);
  if (!hospital) {
    return false;
  }
  
  // Check services array in settings
  if (hospital.settings && Array.isArray(hospital.settings.services)) {
    return hospital.settings.services.includes('whatsapp');
  }
  
  // Check for whatsapp object as fallback
  return !!hospital.whatsapp;
};

/**
 * Normalize phone number for comparison
 * @param {string} phone - Phone number
 * @returns {string} - Normalized phone number
 */
function normalizePhoneNumber(phone) {
  if (!phone) return '';
  
  // Remove all non-digit characters
  return phone.replace(/\D/g, '');
}

/**
 * Get hospital configuration for a specific hospital
 * @param {string} hospitalId - Hospital ID
 * @returns {Object} - Hospital configuration
 */
export const getHospitalConfig = (hospitalId) => {
  return getHospitalById(hospitalId);
};

export default {
  loadHospitals,
  getHospitals,
  getHospitalById,
  getHospitalByPhone,
  getHospitalDbConnection,
  hasWhatsAppService,
  normalizePhoneNumber,
  getHospitalConfig
};
