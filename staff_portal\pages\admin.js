import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import {
  User,
  Users,
  UserPlus,
  Edit,
  Trash2,
  RefreshCw,
  Settings,
  ArrowLeft,
  Save,
  Bell,
  AlertCircle,
  Calendar,
  Clock
} from 'react-feather';
import Layout from '../components/Layout';
import DoctorBookingLimits from '../components/DoctorBookingLimits';
import AvailabilityManagement from '../components/AvailabilityManagement';

export default function AdminPage() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [tab, setTab] = useState('staff'); // 'staff', 'doctors', 'booking-limits', 'settings'
  const [staff, setStaff] = useState([]);
  const [doctors, setDoctors] = useState([]);
  const [hospitalSettings, setHospitalSettings] = useState({
    name: '',
    address: '',
    phone: '',
    email: '',
    enableSmsReminders: true,
    enableEmailReminders: false,
    reminderHoursBeforeAppointment: 24,
    enableTestResultNotifications: true
  });
  
  // Staff editing
  const [editingStaff, setEditingStaff] = useState(null);
  const [newStaff, setNewStaff] = useState({
    name: '',
    email: '',
    role: 'receptionist',
    password: ''
  });

  // Doctor editing
  const [editingDoctor, setEditingDoctor] = useState(null);
  const [newDoctor, setNewDoctor] = useState({
    name: '',
    specialty: '',
    email: '',
    phone: '',
    availableDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
  });

  // Get user status on load
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const res = await fetch('/api/auth/status');
        const data = await res.json();
        
        if (data.success) {
          setUser(data.user);

          // Check if user is admin or receptionist (both can access booking limits)
          if (data.user.role !== 'admin' && data.user.role !== 'receptionist') {
            alert('You do not have permission to access this page');
            router.push('/dashboard');
            return;
          }

          // Set default tab based on role
          if (data.user.role === 'receptionist') {
            setTab('booking-limits'); // Receptionists start with booking limits
          } else {
            setTab('staff'); // Admins start with staff management
          }

          // Fetch data
          fetchStaff(data.user.hospital_id);
          fetchDoctors(data.user.hospital_id);
          if (data.user.role === 'admin') {
            fetchHospitalSettings(data.user.hospital_id);
          }
        } else {
          // Redirect to login if not authenticated
          router.push('/login');
        }
      } catch (error) {
        console.error('Auth check error:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };
    
    checkAuth();
  }, [router]);

  // Fetch staff for this hospital
  const fetchStaff = async (hospitalId) => {
    try {
      setLoading(true);
      const res = await fetch(`/api/admin/staff?hospital_id=${hospitalId}`);
      const data = await res.json();
      
      if (data.success) {
        setStaff(data.data);
      } else {
        console.error('Failed to fetch staff:', data.message);
      }
    } catch (error) {
      console.error('Fetch staff error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch doctors for this hospital
  const fetchDoctors = async (hospitalId) => {
    try {
      setLoading(true);
      const res = await fetch(`/api/doctors?hospital_id=${hospitalId}`);
      const data = await res.json();
      
      if (data.success) {
        setDoctors(data.data);
      } else {
        console.error('Failed to fetch doctors:', data.message);
      }
    } catch (error) {
      console.error('Fetch doctors error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch hospital settings
  const fetchHospitalSettings = async (hospitalId) => {
    try {
      setLoading(true);
      const res = await fetch(`/api/admin/settings?hospital_id=${hospitalId}`);
      const data = await res.json();
      
      if (data.success) {
        setHospitalSettings(data.data);
      } else {
        console.error('Failed to fetch hospital settings:', data.message);
      }
    } catch (error) {
      console.error('Fetch hospital settings error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Save hospital settings
  const saveHospitalSettings = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      const res = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          hospitalId: user.hospital_id,
          settings: hospitalSettings
        })
      });
      
      const data = await res.json();
      
      if (data.success) {
        alert('Hospital settings saved successfully');
      } else {
        alert(`Failed to save settings: ${data.message}`);
      }
    } catch (error) {
      console.error('Save hospital settings error:', error);
      alert('An error occurred while saving settings');
    } finally {
      setLoading(false);
    }
  };

  // Create new staff member
  const createStaffMember = async () => {
    if (!user || !newStaff.name || !newStaff.email || !newStaff.password) {
      alert('Please fill in all required fields');
      return;
    }
    
    try {
      setLoading(true);
      const res = await fetch('/api/admin/staff', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          hospitalId: user.hospital_id,
          staffData: {
            ...newStaff,
            hospital_id: user.hospital_id
          }
        })
      });
      
      const data = await res.json();
      
      if (data.success) {
        alert('Staff member created successfully');
        // Reset form and refresh list
        setNewStaff({
          name: '',
          email: '',
          role: 'receptionist',
          password: ''
        });
        fetchStaff(user.hospital_id);
      } else {
        alert(`Failed to create staff member: ${data.message}`);
      }
    } catch (error) {
      console.error('Create staff member error:', error);
      alert('An error occurred while creating staff member');
    } finally {
      setLoading(false);
    }
  };

  // Update staff member
  const updateStaffMember = async () => {
    if (!user || !editingStaff) return;
    
    try {
      setLoading(true);
      const res = await fetch(`/api/admin/staff/${editingStaff.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          hospitalId: user.hospital_id,
          staffData: {
            name: editingStaff.name,
            email: editingStaff.email,
            role: editingStaff.role,
            hospital_id: user.hospital_id,
            // Only include password if it's been changed
            ...(editingStaff.password ? { password: editingStaff.password } : {})
          }
        })
      });
      
      const data = await res.json();
      
      if (data.success) {
        alert('Staff member updated successfully');
        setEditingStaff(null);
        fetchStaff(user.hospital_id);
      } else {
        alert(`Failed to update staff member: ${data.message}`);
      }
    } catch (error) {
      console.error('Update staff member error:', error);
      alert('An error occurred while updating staff member');
    } finally {
      setLoading(false);
    }
  };

  // Delete staff member
  const deleteStaffMember = async (staffId) => {
    if (!user || !staffId) return;
    
    if (!confirm('Are you sure you want to delete this staff member? This action cannot be undone.')) {
      return;
    }
    
    try {
      setLoading(true);
      const res = await fetch(`/api/admin/staff/${staffId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          hospitalId: user.hospital_id
        })
      });
      
      const data = await res.json();
      
      if (data.success) {
        alert('Staff member deleted successfully');
        fetchStaff(user.hospital_id);
      } else {
        alert(`Failed to delete staff member: ${data.message}`);
      }
    } catch (error) {
      console.error('Delete staff member error:', error);
      alert('An error occurred while deleting staff member');
    } finally {
      setLoading(false);
    }
  };

  // Create new doctor
  const createDoctor = async () => {
    if (!user || !newDoctor.name || !newDoctor.specialty || !newDoctor.phone) {
      alert('Please fill in all required fields');
      return;
    }
    
    try {
      setLoading(true);
      const res = await fetch('/api/admin/doctors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          hospitalId: user.hospital_id,
          doctorData: {
            ...newDoctor,
            hospital_id: user.hospital_id
          }
        })
      });
      
      const data = await res.json();
      
      if (data.success) {
        alert('Doctor created successfully');
        // Reset form and refresh list
        setNewDoctor({
          name: '',
          specialty: '',
          email: '',
          phone: '',
          availableDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
        });
        fetchDoctors(user.hospital_id);
      } else {
        alert(`Failed to create doctor: ${data.message}`);
      }
    } catch (error) {
      console.error('Create doctor error:', error);
      alert('An error occurred while creating doctor');
    } finally {
      setLoading(false);
    }
  };

  // Update doctor
  const updateDoctor = async () => {
    if (!user || !editingDoctor) return;
    
    try {
      setLoading(true);
      const res = await fetch(`/api/admin/doctors/${editingDoctor.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          hospitalId: user.hospital_id,
          doctorData: {
            name: editingDoctor.name,
            specialty: editingDoctor.specialty,
            email: editingDoctor.email,
            phone: editingDoctor.phone,
            availableDays: editingDoctor.availableDays,
            hospital_id: user.hospital_id
          }
        })
      });
      
      const data = await res.json();
      
      if (data.success) {
        alert('Doctor updated successfully');
        setEditingDoctor(null);
        fetchDoctors(user.hospital_id);
      } else {
        alert(`Failed to update doctor: ${data.message}`);
      }
    } catch (error) {
      console.error('Update doctor error:', error);
      alert('An error occurred while updating doctor');
    } finally {
      setLoading(false);
    }
  };

  // Delete doctor
  const deleteDoctor = async (doctorId) => {
    if (!user || !doctorId) return;
    
    if (!confirm('Are you sure you want to delete this doctor? This action cannot be undone.')) {
      return;
    }
    
    try {
      setLoading(true);
      const res = await fetch(`/api/admin/doctors/${doctorId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          hospitalId: user.hospital_id
        })
      });
      
      const data = await res.json();
      
      if (data.success) {
        alert('Doctor deleted successfully');
        fetchDoctors(user.hospital_id);
      } else {
        alert(`Failed to delete doctor: ${data.message}`);
      }
    } catch (error) {
      console.error('Delete doctor error:', error);
      alert('An error occurred while deleting doctor');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-2xl font-semibold text-slate-700 flex items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-teal-500 mr-3"></div>
          Loading...
        </div>
      </div>
    );
  }

  return (
    <Layout title={user?.role === 'admin' ? "Admin Dashboard" : "Staff Dashboard"} user={user}>
      <div className="flex items-center space-x-4 mb-6">
        <Link href="/dashboard" className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-slate-700 bg-slate-100 hover:bg-slate-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Link>
        
      </div>
      
      {/* Tab navigation */}
      <div className="bg-white shadow-sm rounded-lg border border-slate-200 mb-6">
        <div className="px-4 sm:px-6 lg:px-8">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setTab('staff')}
              className={`${
                tab === 'staff'
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              <Users className="inline-block h-5 w-5 mr-2" />
              Staff Management
            </button>
            
            <button
              onClick={() => setTab('doctors')}
              className={`${
                tab === 'doctors'
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              <User className="inline-block h-5 w-5 mr-2" />
              Doctors
            </button>

            <button
              onClick={() => setTab('booking-limits')}
              className={`${
                tab === 'booking-limits'
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              <Calendar className="inline-block h-5 w-5 mr-2" />
              Booking Limits
            </button>

            {/* Only show settings and reminders for admin users */}
            {user?.role === 'admin' && (
              <>
                <button
                  onClick={() => setTab('settings')}
                  className={`${
                    tab === 'settings'
                      ? 'border-teal-500 text-teal-600'
                      : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  <Settings className="inline-block h-5 w-5 mr-2" />
                  Hospital Settings
                </button>

                <button
                  onClick={() => setTab('reminders')}
                  className={`${
                    tab === 'reminders'
                      ? 'border-teal-500 text-teal-600'
                      : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  <Bell className="inline-block h-5 w-5 mr-2" />
                  Reminders
                </button>
              </>
            )}
          </nav>
        </div>
      </div>
      
      {/* Main content */}
      <div>
        {/* Staff Management */}
        {tab === 'staff' && (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-slate-200">
              <h2 className="text-lg font-semibold text-slate-900">Staff Management</h2>
            </div>
            
            {/* Add staff form */}
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h3 className="text-md font-medium text-slate-700 mb-3">Add New Staff Member</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label htmlFor="staff-name" className="block text-sm font-medium text-slate-700">Name *</label>
                  <input
                    type="text"
                    id="staff-name"
                    value={newStaff.name}
                    onChange={(e) => setNewStaff({...newStaff, name: e.target.value})}
                    className="mt-1 p-2 block w-full border border-slate-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="staff-email" className="block text-sm font-medium text-slate-700">Email *</label>
                  <input
                    type="email"
                    id="staff-email"
                    value={newStaff.email}
                    onChange={(e) => setNewStaff({...newStaff, email: e.target.value})}
                    className="mt-1 p-2 block w-full border border-slate-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="staff-role" className="block text-sm font-medium text-slate-700">Role *</label>
                  <select
                    id="staff-role"
                    value={newStaff.role}
                    onChange={(e) => setNewStaff({...newStaff, role: e.target.value})}
                    className="mt-1 p-2 block w-full border border-slate-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                    required
                  >
                    <option value="admin">Admin</option>
                    <option value="doctor">Doctor</option>
                    <option value="receptionist">Receptionist</option>
                    <option value="nurse">Nurse</option>
                    <option value="lab_technician">Lab Technician</option>
                  </select>
                </div>
                
                <div>
                  <label htmlFor="staff-password" className="block text-sm font-medium text-slate-700">Password *</label>
                  <input
                    type="password"
                    id="staff-password"
                    value={newStaff.password}
                    onChange={(e) => setNewStaff({...newStaff, password: e.target.value})}
                    className="mt-1 p-2 block w-full border border-slate-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                    required
                  />
                </div>
              </div>
              
              <div className="mt-4">
                <button
                  onClick={createStaffMember}
                  className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add Staff Member
                </button>
              </div>
            </div>
            
            {/* Staff list */}
            <div className="px-6 py-4">
              <h3 className="text-md font-medium text-slate-700 mb-3">Staff Members</h3>
              
              {staff.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-slate-500">No staff members found.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-slate-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Name</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Email</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Role</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-slate-200">
                      {staff.map((staffMember) => (
                        <tr key={staffMember.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">{staffMember.name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">{staffMember.email}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium 
                              ${staffMember.role === 'admin' ? 'bg-red-100 text-red-800' : ''}
                              ${staffMember.role === 'doctor' ? 'bg-blue-100 text-blue-800' : ''}
                              ${staffMember.role === 'receptionist' ? 'bg-green-100 text-green-800' : ''}
                              ${staffMember.role === 'nurse' ? 'bg-purple-100 text-purple-800' : ''}
                              ${staffMember.role === 'lab_technician' ? 'bg-yellow-100 text-yellow-800' : ''}
                            `}>
                              {staffMember.role.replace('_', ' ')}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button
                              onClick={() => setEditingStaff(staffMember)}
                              className="text-indigo-600 hover:text-indigo-900 mr-4"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => deleteStaffMember(staffMember.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
            
            {/* Edit staff modal */}
            {editingStaff && (
              <div className="fixed inset-0 z-50 overflow-y-auto bg-gray-500 bg-opacity-75 flex items-center justify-center">
                <div className="bg-white rounded-lg overflow-hidden shadow-xl max-w-lg w-full">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900">Edit Staff Member</h3>
                  </div>
                  
                  <div className="px-6 py-4">
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="edit-staff-name" className="block text-sm font-medium text-gray-700">Name</label>
                        <input
                          type="text"
                          id="edit-staff-name"
                          value={editingStaff.name}
                          onChange={(e) => setEditingStaff({...editingStaff, name: e.target.value})}
                          className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="edit-staff-email" className="block text-sm font-medium text-gray-700">Email</label>
                        <input
                          type="email"
                          id="edit-staff-email"
                          value={editingStaff.email}
                          onChange={(e) => setEditingStaff({...editingStaff, email: e.target.value})}
                          className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="edit-staff-role" className="block text-sm font-medium text-gray-700">Role</label>
                        <select
                          id="edit-staff-role"
                          value={editingStaff.role}
                          onChange={(e) => setEditingStaff({...editingStaff, role: e.target.value})}
                          className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                          <option value="admin">Admin</option>
                          <option value="doctor">Doctor</option>
                          <option value="receptionist">Receptionist</option>
                          <option value="nurse">Nurse</option>
                          <option value="lab_technician">Lab Technician</option>
                        </select>
                      </div>
                      
                      <div>
                        <label htmlFor="edit-staff-password" className="block text-sm font-medium text-gray-700">
                          New Password (leave blank to keep unchanged)
                        </label>
                        <input
                          type="password"
                          id="edit-staff-password"
                          value={editingStaff.password || ''}
                          onChange={(e) => setEditingStaff({...editingStaff, password: e.target.value || null})}
                          className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
                    <button
                      onClick={() => setEditingStaff(null)}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={updateStaffMember}
                      className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                    >
                      Save Changes
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Doctors */}
        {tab === 'doctors' && (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Doctors Management</h2>
            </div>
            
            {/* Add doctor form */}
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h3 className="text-md font-medium text-gray-700 mb-3">Add New Doctor</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label htmlFor="doctor-name" className="block text-sm font-medium text-gray-700">Name *</label>
                  <input
                    type="text"
                    id="doctor-name"
                    value={newDoctor.name}
                    onChange={(e) => setNewDoctor({...newDoctor, name: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="doctor-specialty" className="block text-sm font-medium text-gray-700">Specialty *</label>
                  <input
                    type="text"
                    id="doctor-specialty"
                    value={newDoctor.specialty}
                    onChange={(e) => setNewDoctor({...newDoctor, specialty: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="doctor-email" className="block text-sm font-medium text-gray-700">Email</label>
                  <input
                    type="email"
                    id="doctor-email"
                    value={newDoctor.email}
                    onChange={(e) => setNewDoctor({...newDoctor, email: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="doctor-phone" className="block text-sm font-medium text-gray-700">Phone *</label>
                  <input
                    type="tel"
                    id="doctor-phone"
                    value={newDoctor.phone}
                    onChange={(e) => setNewDoctor({...newDoctor, phone: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required
                  />
                </div>
                
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700">Available Days</label>
                  <div className="mt-2 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-7 gap-2">
                    {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
                      <label key={day} className="inline-flex items-center">
                        <input
                          type="checkbox"
                          checked={newDoctor.availableDays.includes(day)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setNewDoctor({
                                ...newDoctor, 
                                availableDays: [...newDoctor.availableDays, day]
                              });
                            } else {
                              setNewDoctor({
                                ...newDoctor, 
                                availableDays: newDoctor.availableDays.filter(d => d !== day)
                              });
                            }
                          }}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">{day}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="mt-4">
                <button
                  onClick={createDoctor}
                  className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add Doctor
                </button>
              </div>
            </div>
            
            {/* Doctors list */}
            <div className="px-6 py-4">
              <h3 className="text-md font-medium text-gray-700 mb-3">Doctors</h3>
              
              {doctors.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-gray-500">No doctors found.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Specialty</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Available Days</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {doctors.map((doctor) => (
                        <tr key={doctor.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Dr. {doctor.name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{doctor.specialty}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div>{doctor.phone}</div>
                            <div className="text-xs">{doctor.email}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div className="flex flex-wrap gap-1">
                              {doctor.availableDays?.map((day) => (
                                <span key={day} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  {day.substring(0, 3)}
                                </span>
                              ))}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button
                              onClick={() => setEditingDoctor(doctor)}
                              className="text-indigo-600 hover:text-indigo-900 mr-4"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => deleteDoctor(doctor.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
            
            {/* Edit doctor modal */}
            {editingDoctor && (
              <div className="fixed inset-0 z-50 overflow-y-auto bg-gray-500 bg-opacity-75 flex items-center justify-center">
                <div className="bg-white rounded-lg overflow-hidden shadow-xl max-w-lg w-full">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900">Edit Doctor</h3>
                  </div>
                  
                  <div className="px-6 py-4">
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="edit-doctor-name" className="block text-sm font-medium text-gray-700">Name</label>
                        <input
                          type="text"
                          id="edit-doctor-name"
                          value={editingDoctor.name}
                          onChange={(e) => setEditingDoctor({...editingDoctor, name: e.target.value})}
                          className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="edit-doctor-specialty" className="block text-sm font-medium text-gray-700">Specialty</label>
                        <input
                          type="text"
                          id="edit-doctor-specialty"
                          value={editingDoctor.specialty}
                          onChange={(e) => setEditingDoctor({...editingDoctor, specialty: e.target.value})}
                          className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="edit-doctor-email" className="block text-sm font-medium text-gray-700">Email</label>
                        <input
                          type="email"
                          id="edit-doctor-email"
                          value={editingDoctor.email || ''}
                          onChange={(e) => setEditingDoctor({...editingDoctor, email: e.target.value})}
                          className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="edit-doctor-phone" className="block text-sm font-medium text-gray-700">Phone</label>
                        <input
                          type="tel"
                          id="edit-doctor-phone"
                          value={editingDoctor.phone}
                          onChange={(e) => setEditingDoctor({...editingDoctor, phone: e.target.value})}
                          className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Available Days</label>
                        <div className="mt-2 grid grid-cols-2 sm:grid-cols-4 gap-2">
                          {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
                            <label key={day} className="inline-flex items-center">
                              <input
                                type="checkbox"
                                checked={editingDoctor.availableDays?.includes(day) || false}
                                onChange={(e) => {
                                  const availableDays = editingDoctor.availableDays || [];
                                  if (e.target.checked) {
                                    setEditingDoctor({
                                      ...editingDoctor, 
                                      availableDays: [...availableDays, day]
                                    });
                                  } else {
                                    setEditingDoctor({
                                      ...editingDoctor, 
                                      availableDays: availableDays.filter(d => d !== day)
                                    });
                                  }
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="ml-2 text-sm text-gray-700">{day}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
                    <button
                      onClick={() => setEditingDoctor(null)}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={updateDoctor}
                      className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                    >
                      Save Changes
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Booking Limits Management */}
        {tab === 'booking-limits' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">Booking Limits Management</h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Manage daily appointment limits for doctors. Changes are automatically synced with the voice agent system.
                  </p>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Clock className="w-4 h-4" />
                  <span>Real-time sync enabled</span>
                </div>
              </div>

              {/* Role-based access notice */}
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">Access Information</h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <p>
                        {user?.role === 'admin'
                          ? 'As an admin, you can modify booking limits for all doctors in your hospital.'
                          : 'As a receptionist, you can modify booking limits to help manage daily appointments efficiently.'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Booking Limits Component */}
              <DoctorBookingLimits
                hospitalId={user?.hospital_id}
                userRole={user?.role}
              />
            </div>

            {/* Availability Management Component */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">Daily Availability Management</h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Control which doctors and tests are available on specific dates. Changes are automatically synced with the voice agent system.
                  </p>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Clock className="w-4 h-4" />
                  <span>Real-time sync enabled</span>
                </div>
              </div>

              {/* Role-based access notice */}
              <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">Availability Control</h3>
                    <div className="mt-2 text-sm text-green-700">
                      <p>
                        {user?.role === 'admin'
                          ? 'As an admin, you can control the daily availability of all doctors and tests in your hospital.'
                          : 'As a receptionist, you can manage daily availability to help patients get accurate information during voice calls.'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Availability Management Component */}
              <AvailabilityManagement
                hospitalId={user?.hospital_id}
                userRole={user?.role}
              />
            </div>
          </div>
        )}

        {/* Hospital Settings - Only for Admin */}
        {tab === 'settings' && user?.role === 'admin' && (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Hospital Settings</h2>
            </div>
            
            <div className="px-6 py-4">
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label htmlFor="hospital-name" className="block text-sm font-medium text-gray-700">Hospital Name</label>
                  <input
                    type="text"
                    id="hospital-name"
                    value={hospitalSettings.name}
                    onChange={(e) => setHospitalSettings({...hospitalSettings, name: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="hospital-address" className="block text-sm font-medium text-gray-700">Address</label>
                  <input
                    type="text"
                    id="hospital-address"
                    value={hospitalSettings.address}
                    onChange={(e) => setHospitalSettings({...hospitalSettings, address: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="hospital-phone" className="block text-sm font-medium text-gray-700">Phone</label>
                  <input
                    type="tel"
                    id="hospital-phone"
                    value={hospitalSettings.phone}
                    onChange={(e) => setHospitalSettings({...hospitalSettings, phone: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="hospital-email" className="block text-sm font-medium text-gray-700">Email</label>
                  <input
                    type="email"
                    id="hospital-email"
                    value={hospitalSettings.email}
                    onChange={(e) => setHospitalSettings({...hospitalSettings, email: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>
              
              <div className="mt-6">
                <h3 className="text-md font-medium text-gray-700 mb-3">Notification Settings</h3>
                
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="enable-sms-reminders"
                        type="checkbox"
                        checked={hospitalSettings.enableSmsReminders}
                        onChange={(e) => setHospitalSettings({...hospitalSettings, enableSmsReminders: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="enable-sms-reminders" className="font-medium text-gray-700">Enable SMS Reminders</label>
                      <p className="text-gray-500">Send SMS reminders to patients about their appointments</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="enable-email-reminders"
                        type="checkbox"
                        checked={hospitalSettings.enableEmailReminders}
                        onChange={(e) => setHospitalSettings({...hospitalSettings, enableEmailReminders: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="enable-email-reminders" className="font-medium text-gray-700">Enable Email Reminders</label>
                      <p className="text-gray-500">Send email reminders to patients about their appointments</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="enable-test-result-notifications"
                        type="checkbox"
                        checked={hospitalSettings.enableTestResultNotifications}
                        onChange={(e) => setHospitalSettings({...hospitalSettings, enableTestResultNotifications: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="enable-test-result-notifications" className="font-medium text-gray-700">Enable Test Result Notifications</label>
                      <p className="text-gray-500">Notify patients when their test results are available</p>
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="reminder-hours" className="block text-sm font-medium text-gray-700">Reminder Hours Before Appointment</label>
                    <select
                      id="reminder-hours"
                      value={hospitalSettings.reminderHoursBeforeAppointment}
                      onChange={(e) => setHospitalSettings({...hospitalSettings, reminderHoursBeforeAppointment: parseInt(e.target.value)})}
                      className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    >
                      <option value="2">2 hours</option>
                      <option value="4">4 hours</option>
                      <option value="12">12 hours</option>
                      <option value="24">24 hours</option>
                      <option value="48">48 hours</option>
                    </select>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 flex justify-end">
                <button
                  onClick={saveHospitalSettings}
                  className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Settings
                </button>
              </div>
            </div>
          </div>
        )}
        
        {/* Reminders - Only for Admin */}
        {tab === 'reminders' && user?.role === 'admin' && (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Reminder System</h2>
            </div>
            
            <div className="px-6 py-4">
              <p className="text-gray-600 mb-4">
                Configure how reminders are sent to patients for appointments and medical tests.
              </p>
              
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">Important Information</h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>
                        Make sure you have configured your Twilio account details in the hospital settings to enable SMS reminders.
                        Email reminders require a valid SMTP server configuration.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Doctor Arrival Notifications</h3>
                  
                  <p className="text-sm text-gray-600 mb-4">
                    When a doctor is running late, use this system to notify all scheduled patients for that doctor.
                  </p>
                  
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-blue-800">How it works</h3>
                        <div className="mt-2 text-sm text-blue-700">
                          <p>
                            When a doctor arrival status is updated as "late," the system will automatically
                            send notifications to all patients with appointments for that doctor on the current day.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <Link href="/dashboard" className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                      <Bell className="mr-2 h-4 w-4" />
                      Go to Doctor Status Dashboard
                    </Link>
                  </div>
                </div>
                
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Appointment Reminders</h3>
                  
                  <p className="text-sm text-gray-600 mb-4">
                    Automate sending reminders to patients about their upcoming appointments.
                  </p>
                  
                  <div className="mt-4 space-y-3">
                    <div className="flex items-start">
                      <div className="flex items-center h-5">
                        <input
                          id="auto-reminder"
                          type="checkbox"
                          checked={hospitalSettings.autoSendReminders}
                          onChange={(e) => setHospitalSettings({...hospitalSettings, autoSendReminders: e.target.checked})}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="auto-reminder" className="font-medium text-gray-700">Enable Automatic Reminders</label>
                        <p className="text-gray-500">System will automatically send reminders based on your settings</p>
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-2">
                      <label htmlFor="reminder-template" className="block text-sm font-medium text-gray-700">Reminder Message Template</label>
                      <textarea
                        id="reminder-template"
                        rows="3"
                        value={hospitalSettings.reminderTemplate || "Dear {patient_name}, this is a reminder about your appointment with Dr. {doctor_name} on {appointment_date} at {appointment_time}. Please arrive 15 minutes early. Contact us at {hospital_phone} if you need to reschedule."}
                        onChange={(e) => setHospitalSettings({...hospitalSettings, reminderTemplate: e.target.value})}
                        className="p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      ></textarea>
                      <p className="text-xs text-gray-500">
                        You can use the following placeholders: {'{patient_name}'}, {'{doctor_name}'}, {'{appointment_date}'}, {'{appointment_time}'}, {'{hospital_name}'}, {'{hospital_phone}'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <button
                      onClick={saveHospitalSettings}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                    >
                      <Save className="mr-2 h-4 w-4" />
                      Save Reminder Settings
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}