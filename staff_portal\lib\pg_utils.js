import { doc, getDoc } from 'firebase/firestore';
import { db as firestoreDb } from './firebase'; // Assuming 'db' is your Firestore instance exported from firebase.js
import pg from 'pg';
import { logger } from './logger';

const { Pool } = pg;

// In-memory cache for hospital-specific connection pools
const hospitalPoolCache = {};

// Standard Pool configuration options (can be adjusted)
const defaultPoolConfig = {
  max: 10, // Max number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
  connectionTimeoutMillis: 5000, // How long to wait for a connection to be established
};

/**
 * Retrieves a PostgreSQL connection pool for a specific hospital.
 * Fetches the hospital's PostgreSQL connection string from Firestore and uses it to create a pool.
 * Caches pools to avoid re-creating them for the same hospital repeatedly.
 *
 * @param {string} hospitalId The ID of the hospital.
 * @returns {Promise<pg.Pool>} A promise that resolves to a pg.Pool instance.
 * @throws {Error} If the hospital is not found, or connection string is missing, or pool creation fails.
 */
export async function getHospitalDbPool(hospitalId) {
  if (!hospitalId) {
    throw new Error('Hospital ID is required to get a database pool.');
  }

  // Check cache first
  if (hospitalPoolCache[hospitalId]) {
    return hospitalPoolCache[hospitalId];
  }

  try {
    // Construct the correct document ID, e.g., 'hospital_123_data'
    const hospitalDocumentId = `hospital_${hospitalId}_data`;
    logger.info(`[PG_UTIL] Attempting to fetch DB connection string for hospital document: ${hospitalDocumentId}`);

    const hospitalDocRef = doc(firestoreDb, 'hospitals', hospitalDocumentId);
    const hospitalSnap = await getDoc(hospitalDocRef);

    if (!hospitalSnap.exists()) {
      logger.error(`[PG_UTIL] Hospital document '${hospitalDocumentId}' not found in Firestore 'hospitals' collection.`);
      throw new Error(`Hospital with ID '${hospitalId}' (document '${hospitalDocumentId}') not found in Firestore.`);
    }

    const hospitalData = hospitalSnap.data();
    const connectionString = hospitalData.db_connection_string;

    if (!connectionString) {
      logger.error(`[PG_UTIL] DB connection string not found for hospital document '${hospitalDocumentId}'.`);
      throw new Error(`DB connection string not found for hospital ${hospitalId}.`);
    }

    logger.info(`[PG_UTIL] Creating new pool for hospital ${hospitalId} (document ${hospitalDocumentId})`);
    const newPool = new Pool({
      connectionString,
      ...defaultPoolConfig,
    });

    // Optional: Test the connection to ensure the pool is valid
    try {
      const client = await newPool.connect();
      logger.info(`[PG_UTIL] Successfully connected to PostgreSQL for hospital ${hospitalId}.`);
      client.release();
    } catch (connectionError) {
      logger.error(`[PG_UTIL] Failed to connect to PostgreSQL for hospital ${hospitalId} using string: ${connectionString.substring(0, connectionString.indexOf('@'))}...`, connectionError);
      // Don't cache a pool that failed to connect initially
      throw new Error(`Failed to establish connection for hospital ${hospitalId}: ${connectionError.message}`);
    }

    hospitalPoolCache[hospitalId] = newPool;
    return newPool;

  } catch (error) {
    logger.error(`[PG_UTIL] Error getting/creating pool for hospital ${hospitalId}: ${error.message}`);
    // Ensure the original error message is part of the new error for better context upstream
    throw new Error(`Failed to establish connection for hospital ${hospitalId}: ${error.message}`);
  }
}

/**
 * Optional: Function to close a specific hospital's pool or all cached pools.
 * This might be useful for graceful shutdowns or if a connection string changes.
 */
export async function closeHospitalDbPool(hospitalId) {
  if (hospitalId && hospitalPoolCache[hospitalId]) {
    logger.info(`[PG_UTIL] Closing PostgreSQL pool for hospital ${hospitalId}`);
    await hospitalPoolCache[hospitalId].end();
    delete hospitalPoolCache[hospitalId];
  } else if (!hospitalId) {
    // Close all cached pools
    logger.info('[PG_UTIL] Closing all cached PostgreSQL pools...');
    for (const id in hospitalPoolCache) {
      await hospitalPoolCache[id].end();
      delete hospitalPoolCache[id];
    }
  }
}

/**
 * Searches PostgreSQL for patients, appointments, and test bookings for a given hospital and search term.
 *
 * @param {string} hospitalId The ID of the hospital.
 * @param {string} searchTerm The term to search for.
 * @returns {Promise<object>} An object containing search results or an error.
 */
export async function searchPostgres(hospitalId, searchTerm) {
  if (!hospitalId || !searchTerm) {
    logger.warn('[PG_UTIL_SEARCH] Hospital ID and search term are required.');
    return { success: false, message: 'Hospital ID and search term are required.', data: { patients: [], appointments: [], testBookings: [] } };
  }

  const searchTermLike = `%${searchTerm}%`;
  let pool;

  try {
    pool = await getHospitalDbPool(hospitalId);
  } catch (poolError) {
    logger.error(`[PG_UTIL_SEARCH] Error getting pool for hospital ${hospitalId}: ${poolError.message}`);
    return { success: false, message: `Database connection error: ${poolError.message}`, data: { patients: [], appointments: [], testBookings: [] } };
  }

  try {
    const client = await pool.connect();
    try {
      // Search Patients
      // Assuming 'patients' table has 'name', 'phone', 'email' columns. Adjust as needed.
      const patientQuery = {
        text: `SELECT id, name, phone, email, created_at, updated_at 
               FROM patients 
               WHERE hospital_id = $1 AND (name ILIKE $2 OR phone ILIKE $2 OR email ILIKE $2)`,
        values: [hospitalId, searchTermLike],
      };
      const patientResults = await client.query(patientQuery);

      // Search Appointments
      // Assuming 'appointments' table has 'id', 'patient_id', 'doctor_id', 'start_time', 'status', 'notes'
      // And patients table has 'name' as patient_name, doctors table has 'name' as doctor_name
      // This query is more complex due to joins. Adjust table and column names as per your schema.
      const appointmentQuery = {
        text: `SELECT a.id, a.patient_id, a.doctor_id, p.name as patient_name, 
                      a.start_time, a.end_time, a.status, a.source, a.notes 
               FROM appointments a
               LEFT JOIN patients p ON a.patient_id = p.id AND p.hospital_id = a.hospital_id
               WHERE a.hospital_id = $1 AND 
                     (p.name ILIKE $2 OR 
                      a.notes ILIKE $2 OR 
                      a.status ILIKE $2)
               ORDER BY a.start_time DESC LIMIT 10`,
        values: [hospitalId, searchTermLike],
      };
      const appointmentResults = await client.query(appointmentQuery);

      // Search Test Bookings
      // Assuming 'test_bookings' table has 'id', 'patient_name', 'test_type_id' (as string), 'booking_time', 'status'
      const testBookingQuery = {
        text: `SELECT tb.id, tb.patient_name, tb.phone, tb.test_type_id, 
                      tb.booking_time, tb.status, tb.notes 
               FROM test_bookings tb
               WHERE tb.hospital_id = $1 AND 
                     (tb.patient_name ILIKE $2 OR 
                      tb.phone ILIKE $2 OR 
                      tb.test_type_id ILIKE $2 OR 
                      tb.status ILIKE $2 OR 
                      tb.notes ILIKE $2 OR 
                      CAST(tb.booking_time AS TEXT) ILIKE $2)`, 
                      // Simple text search on timestamp
        values: [hospitalId, searchTermLike],
      };
      const testBookingResults = await client.query(testBookingQuery);

      logger.info(`[PG_UTIL_SEARCH] Search successful for hospital ${hospitalId} with term '${searchTerm}'. Found ${patientResults.rows.length} patients, ${appointmentResults.rows.length} appointments, ${testBookingResults.rows.length} test bookings.`);
      return {
        success: true,
        data: {
          patients: patientResults.rows,
          appointments: appointmentResults.rows,
          testBookings: testBookingResults.rows,
        },
      };
    } finally {
      client.release();
    }
  } catch (dbError) {
    logger.error(`[PG_UTIL_SEARCH] Database query error for hospital ${hospitalId} with term '${searchTerm}': ${dbError.message}`, dbError);
    return { success: false, message: `Database query error: ${dbError.message}`, data: { patients: [], appointments: [], testBookings: [] } };
  }
}

// Example of listening for app termination to close pools (e.g., in your main server file if not Next.js API routes)
// process.on('SIGINT', async () => {
//   await closeHospitalDbPool();
//   process.exit(0);
// });
// process.on('SIGTERM', async () => {
//   await closeHospitalDbPool();
//   process.exit(0);
// });
