/**
 * WhatsApp Agent Server for Hospital Management System
 * 
 * This server handles incoming WhatsApp messages via Twilio's WhatsApp Business API,
 * processes them using the same GPT-4o Mini model as the voice agent,
 * and integrates with the existing hospital management system.
 */

import express from 'express';
import dotenv from 'dotenv';
import bodyParser from 'body-parser';
import cors from 'cors';
import { logger } from './lib/logger.js';
import whatsappRoutes from './routes/whatsapp.js';
import { initializeFirebase } from './lib/firebase.js';
import { initializeRedis } from './lib/redis.js';
import { loadHospitals } from './lib/hospitals.js';

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();
const PORT = process.env.WHATSAPP_PORT || 3002;

// Middleware
app.use(cors());
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', service: 'whatsapp-agent' });
});

// WhatsApp webhook routes
app.use('/api/whatsapp', whatsappRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Server error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Initialize services and start server
async function startServer() {
  try {
    // Initialize Firebase
    await initializeFirebase();
    logger.info('Firebase initialized successfully');
    
    // Initialize Redis
    await initializeRedis();
    logger.info('Redis connection established');
    
    // Load hospital configurations
    await loadHospitals();
    logger.info('Hospital configurations loaded');
    
    // Start the server
    app.listen(PORT, () => {
      logger.info(`WhatsApp agent server running on port ${PORT}`);
      logger.info('Ready to process WhatsApp messages');
    });
  } catch (error) {
    logger.error('Failed to start WhatsApp agent server:', error);
    process.exit(1);
  }
}

startServer();
