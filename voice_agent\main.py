import os
import json
import async<PERSON>
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
import logging

from .call_context import CallContext # Import new CallContext
from .database import get_firestore_db, get_postgres_connection, get_doctors, get_tests
from .telephony import handle_call, setup_jambonz
from .models import HospitalConfig, SIPTrunk
from .nlp import process_speech
from .utils import create_ssh_tunnel, close_ssh_tunnel, record_call
from .jambonz_service import jambonz_service
from .semantic_integration import semantic_engine, process_voice_query
from .language_config import (
    language_config, get_primary_language, get_supported_languages,
    detect_language, get_speech_code, get_language_name
)
# Import new LLM integration
from .llm_integration import voice_llm_integration, process_voice_input, extract_booking_intent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Lifespan event handler for FastAPI application.
    Replaces the deprecated @app.on_event("startup") and @app.on_event("shutdown") decorators.
    """
    # Startup logic
    logger.info("Starting Megha Voice Agent with Semantic Caching")

    # Test Redis connection using centralized pool
    try:
        pool = RedisConnectionPool()
        async_client = pool.get_async_client()
        if async_client:
            await async_client.ping()
            logger.info("Redis connection successful (centralized pool)")
        else:
            logger.error("Failed to get async Redis client from centralized pool")
    except Exception as e:
        logger.error(f"Redis connection failed (centralized pool): {e}")

    # Proactively test async Redis pools used by semantic engine
    try:
        logger.info("Testing semantic engine async Redis connections...")
        redis_ready = await semantic_engine.ensure_async_pool_ready()
        if redis_ready:
            logger.info("✅ Semantic engine async Redis pools are ready")
        else:
            logger.error("❌ Semantic engine async Redis pools failed readiness test")
            logger.warning("⚠️ This may cause runtime failures during voice interactions")
    except Exception as e:
        logger.error(f"❌ Error testing semantic engine async Redis pools: {e}")
        logger.warning("⚠️ Async Redis connections may fail during runtime")

    # Initialize semantic engine warm-up in the background (non-blocking)
    try:
        async def warmup_semantic_cache():
            try:
                logger.info("Starting semantic cache warm-up process...")

                # Load hospital data dynamically from Firebase
                hospitals_data = await load_hospitals_for_semantic_cache()
                if hospitals_data:
                    logger.info(f"Loaded {len(hospitals_data)} hospitals, starting cache warm-up...")

                    # Warm up the semantic cache
                    success = await semantic_engine.warm_up_cache(hospitals_data)

                    if success:
                        logger.info(f"✅ Semantic cache warmed up successfully for {len(hospitals_data)} hospitals")

                        # Log summary of loaded data
                        total_doctors = sum(len(h.get('doctors', [])) for h in hospitals_data)
                        total_tests = sum(len(h.get('tests', [])) for h in hospitals_data)
                        logger.info(f"Cache contains: {total_doctors} doctors, {total_tests} tests across all hospitals")
                    else:
                        logger.warning("⚠️ Semantic cache warm-up completed with some failures")
                else:
                    logger.warning("⚠️ No hospital data found for semantic cache warm-up")
            except Exception as e:
                logger.error(f"❌ Error warming up semantic cache: {e}")

        app.state.semantic_warmup_task = asyncio.create_task(warmup_semantic_cache())
        logger.info("Started semantic cache warm-up in background task")
    except Exception as e:
        logger.error(f"Error launching semantic cache warm-up task: {e}")

    # Start booking limit scheduler for daily cache refresh
    try:
        from .booking_limit_scheduler import booking_scheduler
        await booking_scheduler.start_scheduler()
        logger.info("✅ Booking limit scheduler started - daily refresh at 12:00 PM")
    except Exception as e:
        logger.error(f"❌ Error starting booking limit scheduler: {e}")

    # Yield control to the application
    yield

    # Shutdown logic
    logger.info("Shutting down Megha Voice Agent")

    # Stop booking limit scheduler
    try:
        from .booking_limit_scheduler import booking_scheduler
        await booking_scheduler.stop_scheduler()
        logger.info("✅ Booking limit scheduler stopped")
    except Exception as e:
        logger.error(f"❌ Error stopping booking limit scheduler: {e}")

    # Cancel and await the semantic warmup task if it's still running
    task = getattr(app.state, "semantic_warmup_task", None)
    if task:
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            logger.info("Semantic warmup task cancelled successfully")
        except Exception as e:
            logger.error(f"Error during semantic warmup task cancellation: {e}")

    # Close all active SSH tunnels
    for hospital_id, tunnel in active_tunnels.items():
        try:
            close_ssh_tunnel(tunnel)
            logger.info(f"Closed SSH tunnel for hospital {hospital_id}")
        except Exception as e:
            logger.error(f"Error closing SSH tunnel for hospital {hospital_id}: {e}")

    # Redis connections are managed by the centralized pool
    # No explicit cleanup needed as the pool handles connection lifecycle
    logger.info("Redis connections managed by centralized pool - no explicit cleanup needed")

app = FastAPI(title="Megha Voice Agent", lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Use centralized Redis connection pool for all Redis operations
# This eliminates duplicate connections and ensures consistent configuration
from .cache_manager import RedisConnectionPool

# Keep track of active SSH tunnels
active_tunnels = {}

async def load_hospitals_for_semantic_cache():
    """
    Load hospital data from Firebase for semantic cache warm-up.
    Returns a list of hospital configurations with doctors and tests data.
    Production-ready with error handling, retry logic, and dynamic data loading.
    """
    max_retries = 3
    base_delay = 2  # seconds

    for attempt in range(max_retries):
        retry_delay = base_delay * (2 ** attempt)  # Exponential backoff: 2, 4, 8 seconds
        try:
            db = get_firestore_db()
            hospitals_data = []

            # Get all hospital documents from Firebase with timeout
            hospitals_collection = db.collection('hospitals')
            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            hospital_docs = await loop.run_in_executor(None, hospitals_collection.stream)

            for hospital_doc in hospital_docs:
                try:
                    hospital_id = None
                    hospital_config = hospital_doc.to_dict()

                    # Extract hospital ID from document ID (format: hospital_{id}_data)
                    doc_id = hospital_doc.id
                    if doc_id.startswith('hospital_') and doc_id.endswith('_data'):
                        hospital_id = doc_id.replace('hospital_', '').replace('_data', '')
                    else:
                        # Fallback: try to get ID from document data
                        hospital_id = hospital_config.get('id')

                    if not hospital_id:
                        logger.warning(f"Skipping hospital document {doc_id}: no valid hospital ID found")
                        continue

                    # Get supported languages for this hospital (with Hindi as primary)
                    hospital_languages = hospital_config.get('languages', language_config.get_supported_languages())

                    # Ensure Hindi is first if available
                    if "hi" in hospital_languages and hospital_languages[0] != "hi":
                        hospital_languages = ["hi"] + [lang for lang in hospital_languages if lang != "hi"]

                    # Load doctors data for this hospital
                    doctors_data = []
                    try:
                        doctors = await get_doctors(hospital_id, force_refresh=True)
                        for doctor in doctors:
                            # Validate doctor data before adding
                            if not doctor.get('name'):
                                logger.warning(f"Skipping doctor with missing name in hospital {hospital_id}: {doctor}")
                                continue

                            # Transform doctor data for semantic cache
                            doctor_info = {
                                "id": doctor.get('id', ''),
                                "name": doctor.get('name', 'Unknown Doctor'),
                                "specialty": doctor.get('specialty', 'General Medicine'),
                                "schedule": doctor.get('schedule', {}),
                                "availability": doctor.get('availability', {}),
                                "price": doctor.get('price', 0.0)
                            }
                            doctors_data.append(doctor_info)

                        logger.info(f"Loaded {len(doctors_data)} doctors for hospital {hospital_id}")
                    except Exception as e:
                        logger.error(f"Error loading doctors for hospital {hospital_id}: {e}")
                        # Continue with empty doctors list - system should still work

                    # Load tests data for this hospital
                    tests_data = []
                    try:
                        tests = await get_tests(hospital_id, force_refresh=True)
                        for test in tests:
                            # Validate test data before adding
                            if not test.get('name'):
                                logger.warning(f"Skipping test with missing name in hospital {hospital_id}: {test}")
                                continue

                            # Transform test data for semantic cache
                            cost = test.get('cost', 0.0)
                            # Ensure cost is numeric
                            try:
                                cost = float(cost) if cost is not None else 0.0
                            except (ValueError, TypeError):
                                logger.warning(f"Invalid cost for test {test.get('name')} in hospital {hospital_id}: {cost}")
                                cost = 0.0

                            test_info = {
                                "id": test.get('id', ''),
                                "name": test.get('name', 'Unknown Test'),
                                "description": test.get('description', ''),
                                "duration": test.get('duration', 30),
                                "cost": cost,
                                "price": f"₹{cost}",  # Format price for display
                                "requirements": test.get('requirements', '')
                            }
                            tests_data.append(test_info)

                        logger.info(f"Loaded {len(tests_data)} tests for hospital {hospital_id}")
                    except Exception as e:
                        logger.error(f"Error loading tests for hospital {hospital_id}: {e}")
                        # Continue with empty tests list - system should still work

                    # Create hospital data structure for semantic cache
                    hospital_data = {
                        "id": hospital_id,
                        "name": hospital_config.get('name', f'Hospital {hospital_id}'),
                        "languages": hospital_languages,
                        "doctors": doctors_data,
                        "tests": tests_data,
                        "settings": hospital_config.get('settings', {}),
                        "address": hospital_config.get('address', ''),
                        "phone": hospital_config.get('phone', ''),
                        "emergency_number": hospital_config.get('emergency_number', '911')
                    }

                    hospitals_data.append(hospital_data)
                    logger.info(f"Successfully loaded hospital {hospital_id} with {len(doctors_data)} doctors and {len(tests_data)} tests")

                except Exception as e:
                    logger.error(f"Error processing hospital document {hospital_doc.id}: {e}")
                    continue

            logger.info(f"Successfully loaded {len(hospitals_data)} hospitals for semantic cache")
            return hospitals_data

        except Exception as e:
            logger.error(f"Attempt {attempt + 1}/{max_retries} failed loading hospitals from Firebase: {e}")
            if attempt == max_retries - 1:
                # Last attempt failed
                logger.error("All attempts failed to load hospitals from Firebase")
                return []
            else:
                # Wait before retry with exponential backoff
                await asyncio.sleep(retry_delay)
                continue

    # This should never be reached, but just in case
    return []

# Startup and shutdown logic moved to lifespan function above

async def get_hospital_config(hospital_id: str) -> HospitalConfig:
    """
    Get hospital configuration from Firestore and establish SSH tunnel if needed
    """
    logger.info(f"Getting hospital configuration for hospital ID: {hospital_id}")
    db = get_firestore_db()
    
    # Path for hospital configuration document is now hospital_{hospital_id}_data
    hospital_doc_id = f'hospital_{hospital_id}_data' # Updated document ID
    logger.info(f"Looking for hospital document at hospitals/{hospital_doc_id}") # Updated logging
    
    try:
        # Get the hospital document from the 'hospitals' collection in a non-blocking way
        loop = asyncio.get_running_loop()
        hospital_doc = await loop.run_in_executor(None, lambda: db.collection('hospitals').document(hospital_doc_id).get())
        
        if hospital_doc.exists:
            logger.info(f"Found hospital config at hospitals/{hospital_doc_id}") # Updated logging
            hospital_data = hospital_doc.to_dict()
            logger.info(f"Hospital data: {hospital_data}")
            
            # If hospital_data doesn't have an 'id' field, add it
            if 'id' not in hospital_data:
                hospital_data['id'] = hospital_id
                
            try:
                config = HospitalConfig(**hospital_data)
                
                # Check if SSH tunnel is needed and not already active
                if hasattr(config, 'ssh_tunnel') and config.ssh_tunnel and hospital_id not in active_tunnels:
                    try:
                        # Create SSH tunnel for PostgreSQL connection with robust port management
                        tunnel = create_ssh_tunnel(
                            ssh_host=config.ssh_tunnel.get('host'),
                            ssh_user=config.ssh_tunnel.get('user'),
                            ssh_private_key=config.ssh_tunnel.get('private_key_path'),
                            remote_host='localhost',  # Assume PostgreSQL is on the same server
                            remote_port=5432,
                            hospital_id=hospital_id  # Let port manager handle port allocation
                        )
                        active_tunnels[hospital_id] = tunnel
                        logger.info(f"Established SSH tunnel for hospital {hospital_id}")
                    except Exception as tunnel_err:
                        logger.error(f"Error creating SSH tunnel: {tunnel_err}")
                        # Continue without tunnel
                
                return config
            except Exception as config_err:
                logger.error(f"Error creating hospital config: {config_err}")
                
        else:
            logger.error(f"Hospital document {hospital_doc_id} does not exist in 'hospitals' collection") # Updated logging (implicitly via hospital_doc_id)
    except Exception as e:
        logger.error(f"Error getting hospital config: {e}")
    
    # If we get here, we couldn't find a valid hospital configuration
    # Updated error message to reflect the new document path format
    logger.error(f"Hospital configuration document 'hospitals/{hospital_doc_id}' not found.")
    
    # Instead of returning a fake hospital config, raise a proper exception
    # This will be caught by the try/except in the calling functions
    # and return an appropriate error message to the caller
    raise HTTPException(
        status_code=404,
        detail=f"Hospital {hospital_id} configuration (hospitals/{hospital_doc_id}) not found. Please contact support." # Updated detail
    )

# Removed old get_call_context and update_call_context functions
# as CallContext class now handles its own persistence.

@app.post("/webhook/call")
async def handle_incoming_call(request: Request):
    """
    Handle incoming call webhook from Jambonz.
    """
    try:
        payload = await request.json()
        logger.info(f"Received call webhook (JSON): {payload}")

        call_id = payload.get('call_id') or str(uuid.uuid4())
        hospital_id = extract_hospital_id_from_did(payload.get('to'))
        caller_number = payload.get('from')
        timestamp = payload.get('time')

        if not hospital_id:
            logger.error(f"Unable to determine hospital ID from DID: {payload.get('to')}")
            return [
                {"verb": "say", "text": "Sorry, unable to determine hospital."},
                {"verb": "hangup"}
            ]
        
        # Get hospital configuration
        hospital_config = await get_hospital_config(hospital_id)
        
        # Initialize call context using the new CallContext.get_or_create
        ctx = await CallContext.get_or_create(
            call_id=call_id,
            hospital_id=hospital_id,
            caller_number=caller_number
        )
        # The 'timestamp' from payload can be stored in ctx.data if needed, e.g.:
        if timestamp:
            await ctx.update_data({"initial_timestamp": timestamp})
        
        # Generate initial response with Hindi as primary language
        # Get supported languages for this hospital (default to Hindi, Bengali, English)
        hospital_languages = getattr(hospital_config, 'languages', None)
        if not hospital_languages:
            # Default to supported Indian languages with Hindi first
            hospital_languages = language_config.get_supported_languages()

        # Ensure Hindi is first if available
        if "hi" in hospital_languages and hospital_languages[0] != "hi":
            hospital_languages = ["hi"] + [lang for lang in hospital_languages if lang != "hi"]

        # Create language selection options in Hindi (primary language)
        language_options = ""
        for idx, lang_code in enumerate(hospital_languages, 1):
            lang_name = language_config.get_language_name(lang_code, "hi")  # Get name in Hindi
            if idx == 1:
                language_options += f", {lang_name} के लिए {idx} दबाएं"
            else:
                language_options += f", {lang_name} के लिए {idx} दबाएं"

        # Welcome message in Hindi (primary language)
        welcome_message = language_config.get_welcome_message(
            "hi",
            hospital_config.name,
            language_options
        )
        
        # Jambonz JSON response
        return [{
            "verb": "gather",
            "actionHook": "/webhook/gather",
            "input": ["speech", "dtmf"],
            "timeout": 10,
            "bargein": True,
            "say": {
                "text": welcome_message
            }
        }]
        
    except HTTPException as e:
        logger.error(f"HTTP exception in webhook/call: {e.detail}")
        error_message = language_config.get_error_message(
            error_type="hospital_not_found",
            language=get_primary_language()  # Default to Hindi since we don't have language context yet
        )
        return [
            {"verb": "say", "text": error_message},
            {"verb": "hangup"}
        ]
            
    except Exception as e:
        logger.error(f"Unexpected error handling incoming call: {e}")
        error_message = "We apologize for the inconvenience. Our system is experiencing technical difficulties. Please try your call again later."
        return [
            {"verb": "say", "text": error_message},
            {"verb": "hangup"}
        ]

@app.post("/webhook/gather")
async def handle_gather(request: Request):
    """
    Handle gathering input from the user
    """
    try:
        payload = await request.json()
        logger.info(f"Received gather webhook: {payload}")

        call_id = payload.get('call_id')
        speech_result = payload.get('speech', {}).get('alternatives', [{}])[0].get('transcript', '')
        dtmf_digits = payload.get('dtmf', {}).get('digits', '')

        if not call_id:
            logger.error("Call ID missing in gather webhook payload.")
            return [{"verb": "hangup"}]

        # Get call context using the new CallContext.get_or_create
        ctx = await CallContext.get_or_create(call_id=call_id)
        if not ctx: # Should ideally not happen if call_id is valid
            logger.error(f"Critical: Failed to get or create context for call_id {call_id} in handle_gather")
            return [{"verb": "say", "text": "Sorry, a system error occurred. Please try your call again."}, {"verb": "hangup"}]

        hospital_id = ctx.hospital_id # Use attribute
        state = ctx.state           # Use attribute
        
        # Get hospital configuration
        hospital_config = await get_hospital_config(hospital_id) # hospital_id from ctx

        # ENHANCED LLM PROCESSING: Use unified LLM service for intelligent voice processing
        if speech_result and len(speech_result.strip()) > 3:
            try:
                # First try semantic cache for quick responses
                semantic_result = await process_voice_query(
                    query=speech_result,
                    hospital_id=hospital_id,
                    language=ctx.language or get_primary_language(),
                    context={"state": state, "caller_number": ctx.caller_number}
                )

                # If we got a high-confidence semantic response, use it
                if semantic_result["confidence"] > 0.8:
                    logger.info(f"Semantic cache hit for query: '{speech_result}' "
                                f"(confidence: {semantic_result['confidence']:.2f}, "
                                f"time: {semantic_result['processing_time_ms']:.1f}ms)")

                    return [{
                        "verb": "say",
                        "text": semantic_result["response"]
                    }, {
                        "verb": "gather",
                        "actionHook": "/webhook/gather",
                        "input": ["speech", "dtmf"],
                        "timeout": 10,
                        "bargein": True,
                        "say": {
                            "text": "Is there anything else I can help you with? You can ask about doctors, tests, or say goodbye to end the call."
                        }
                    }]

                # If semantic cache didn't provide a good response, try LLM processing
                if semantic_result["confidence"] < 0.8:
                    llm_context = {
                        "hospital_id": hospital_id,
                        "language": ctx.language or get_primary_language(),
                        "hospital_name": getattr(hospital_config, 'name', f'Hospital {hospital_id}'),
                        "user_info": {
                            "phone": ctx.caller_number,
                            "call_id": call_id
                        },
                        "state": state,
                        "chat_history": []  # Could be enhanced to include call history
                    }

                    # Process with LLM for intelligent understanding
                    llm_result = await process_voice_input(speech_result, llm_context)

                    if llm_result.get("success") and llm_result.get("response"):
                        logger.info(f"LLM processing successful for query: '{speech_result}'")

                        # Check if LLM identified specific intents or function calls
                        if llm_result.get("function_calls"):
                            # Handle function call results
                            response_text = llm_result["response"]

                            # Add function call results to response if available
                            for func_call in llm_result["function_calls"]:
                                if func_call.get("result") and func_call["result"].get("success"):
                                    # Function call was successful, response should include the results
                                    pass
                        else:
                            response_text = llm_result["response"]

                        return [{
                            "verb": "say",
                            "text": response_text
                        }, {
                            "verb": "gather",
                            "actionHook": "/webhook/gather",
                            "input": ["speech", "dtmf"],
                            "timeout": 10,
                            "bargein": True,
                            "say": {
                                "text": "Is there anything else I can help you with?"
                            }
                        }]

            except Exception as e:
                logger.error(f"Error in LLM processing: {e}")
                # Continue with normal flow if LLM processing fails

        # Handle emergency case (0 pressed)
        if dtmf_digits == '0':
            logger.info(f"Call ID {call_id}: Emergency option 0 selected.")
            # Simplified: Say message and hangup.
            # Or, can redirect to a dedicated emergency webhook as before,
            # but that webhook also needs to be Jambonz compliant.
            # The original Jambonz response was: {"verb": "say", "text": "Connecting...", "actionHook": "/webhook/emergency"}
            # Let's use that, wrapped in an array.
            return [{
                "verb": "say",
                "text": "Connecting you to emergency services. Please stay on the line.",
                "actionHook": "/webhook/emergency" # This will trigger the /webhook/emergency endpoint
            }]

        # Helper function to determine selected language from input
        def determine_language_selection(speech_result: str, dtmf_digits: str, available_languages: list) -> str:
            """Determine which language the user selected based on DTMF or speech input"""
            # Default to Hindi (primary language) if available, otherwise use the first available language
            default_language = get_primary_language()
            if default_language not in available_languages:
                default_language = available_languages[0] if available_languages else "hi"
            
            # Check for DTMF digit selection first - more reliable
            if dtmf_digits and dtmf_digits.isdigit():
                digit = int(dtmf_digits)
                # Map digit to language index (1-based)
                if 1 <= digit <= len(available_languages):
                    return available_languages[digit-1]
            
            # If no DTMF or invalid, try speech recognition with enhanced language detection
            if speech_result:
                # First try automatic language detection from script
                detected_lang = detect_language(speech_result)
                if detected_lang in available_languages:
                    return detected_lang

                # Fallback to keyword matching with enhanced Indian language support
                speech_lower = speech_result.lower()
                # Enhanced language name mapping with regional variations
                language_name_map = {
                    # Hindi variations (primary language)
                    "hindi": "hi", "हिंदी": "hi", "हिन्दी": "hi", "हिंदि": "hi",
                    # Bengali variations
                    "bengali": "bn", "বাংলা": "bn", "bangla": "bn", "bengla": "bn",
                    # English variations
                    "english": "en", "angrezi": "en", "अंग्रेजी": "en",

                    # TODO: Add more language variations when scaling:
                    # "tamil": "ta", "தமிழ்": "ta", "tamizh": "ta",
                    # "telugu": "te", "తెలుగు": "te", "telgu": "te",
                    # "marathi": "mr", "मराठी": "mr", "marathi": "mr",
                    # "gujarati": "gu", "ગુજરાતી": "gu", "gujrati": "gu",
                    # "kannada": "kn", "ಕನ್ನಡ": "kn", "kannad": "kn",
                    # "punjabi": "pa", "ਪੰਜਾਬੀ": "pa", "panjabi": "pa"
                }

                for name, code in language_name_map.items():
                    if name in speech_lower and code in available_languages:
                        return code

            # If no match found, return default language (Hindi)
            return default_language
            
        # State machine for call flow
        if state == "greeting":
            # Language selection with Hindi as primary
            available_languages = getattr(hospital_config, 'languages', None)
            if not available_languages:
                # Default to supported Indian languages with Hindi first
                available_languages = language_config.get_supported_languages()

            # Ensure Hindi is first if available
            if "hi" in available_languages and available_languages[0] != "hi":
                available_languages = ["hi"] + [lang for lang in available_languages if lang != "hi"]

            selected_language = determine_language_selection(speech_result, dtmf_digits, available_languages)
            
            # Log the selection process
            logger.info(f"Language selection: DTMF={dtmf_digits}, Speech={speech_result}, Selected={selected_language}")
            
            if selected_language in available_languages:
                await ctx.set_language(selected_language)
                await ctx.update_state("main_menu")
                
                # Translate main menu options based on selected language
                main_menu_text = await get_translated_text(
                    "Please say 1 or press 1 to book a doctor's appointment. Say 2 or press 2 to book a medical test.",
                    selected_language
                )
                
                # Jambonz JSON response
                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather",
                    "input": ["speech", "dtmf"],
                    "timeout": 10,
                    "bargein": True,
                    "say": {
                        "text": main_menu_text
                    }
                }]
            else:
                # Language not supported, retry
                languages_text = ", ".join([get_language_name(lang) for lang in hospital_config.languages])
                retry_text = f"Sorry, we support {languages_text}. Please select one."
                
                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather", # Stay in greeting state for language selection
                    "input": ["speech", "dtmf"],
                    "timeout": 10,
                    "bargein": True,
                    "say": {
                        "text": retry_text
                    }
                }]
        
        elif state == "main_menu":
            selected_option = determine_menu_selection(speech_result, dtmf_digits)
            
            if selected_option == 1:  # Book doctor appointment
                await ctx.update_state("doctor_booking")

                # Get available doctors from Firestore for today
                from datetime import datetime
                today = datetime.now().strftime('%Y-%m-%d')
                doctors = await get_available_doctors(ctx.hospital_id, today) # Use ctx.hospital_id with date
                doctor_options = await format_doctor_options(doctors, ctx.language) # Use ctx.language

                prompt_text = await get_translated_text(
                    "Please select a doctor by saying their name or specialty, or press the corresponding number.",
                    ctx.language
                )
                
                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather",
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{prompt_text} {doctor_options}"
                    }
                }]
                
            elif selected_option == 2:  # Book test
                await ctx.update_state("test_booking")

                # Get available tests from Firestore for today
                from datetime import datetime
                today = datetime.now().strftime('%Y-%m-%d')
                tests = await get_available_tests(ctx.hospital_id, today) # Use ctx.hospital_id with date
                test_options = await format_test_options(tests, ctx.language) # Use ctx.language

                prompt_text = await get_translated_text(
                    "Please select a test by saying the test name or press the corresponding number.",
                    ctx.language
                )
                
                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather",
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{prompt_text} {test_options}"
                    }
                }]
            else:
                # Invalid option, retry
                retry_text = await get_translated_text(
                    "Sorry, I didn't understand. Please say 1 for doctor appointment or 2 for medical test.",
                    ctx.language # Use ctx.language
                )
                
                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather", # Remain in main_menu state
                    "input": ["speech", "dtmf"],
                    "timeout": 10,
                    "bargein": True,
                    "say": {
                        "text": retry_text
                    }
                }]
        
        elif state == "doctor_booking":
            # Process doctor selection
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')
            doctors = await get_available_doctors(ctx.hospital_id, today) # Use ctx.hospital_id with date
            selected_doctor = match_doctor_selection(speech_result, dtmf_digits, doctors)
            
            if selected_doctor:
                await ctx.update_data({
                    "doctor_id": selected_doctor["id"],
                    "doctor_name": selected_doctor["name"]
                })

                # Check booking availability for today first using Redis for ultra-fast response
                from datetime import datetime
                today = datetime.now().strftime('%Y-%m-%d')

                try:
                    # First try Redis cache for instant response
                    from .cache_manager import redis_manager
                    availability = await redis_manager.check_booking_availability_redis_async(
                        ctx.hospital_id, selected_doctor["id"], today
                    )

                    # If Redis doesn't have the data, fallback to Firebase/PostgreSQL
                    if not availability.get('is_available') and availability.get('limit') == 10:
                        # This indicates default values, try database fallback
                        from .database import check_booking_availability
                        availability = await check_booking_availability(ctx.hospital_id, selected_doctor["id"], today)

                    if not availability['is_available']:
                        # Booking limit reached, offer next available date
                        next_date = availability.get('next_available_date')

                        if next_date:
                            # Format the date for display
                            next_date_obj = datetime.strptime(next_date, '%Y-%m-%d')
                            formatted_date = next_date_obj.strftime('%B %d, %Y')  # e.g., "January 15, 2025"

                            # Get multilingual error messages
                            limit_message = language_config.get_error_message(
                                "booking_limit_reached",
                                ctx.language,
                                doctor_name=selected_doctor["name"]
                            )
                            next_date_message = language_config.get_error_message(
                                "next_available_date",
                                ctx.language,
                                date=formatted_date
                            )
                            choice_message = language_config.get_error_message(
                                "choose_different_option",
                                ctx.language,
                                date=formatted_date
                            )

                            full_message = f"{limit_message} {next_date_message} {choice_message}"

                            # Store the next available date for potential booking
                            await ctx.update_data({"next_available_date": next_date})
                            await ctx.update_state("booking_limit_choice")

                            return [{
                                "verb": "gather",
                                "actionHook": "/webhook/gather",
                                "input": ["speech", "dtmf"],
                                "timeout": 15,
                                "bargein": True,
                                "say": {
                                    "text": full_message
                                }
                            }]
                        else:
                            # No available dates found
                            error_message = language_config.get_error_message(
                                "schedule_not_available",
                                ctx.language
                            )

                            return [
                                {"verb": "say", "text": error_message},
                                {"verb": "hangup"}
                            ]

                except Exception as e:
                    logger.error(f"Error checking booking availability: {e}")
                    # Continue with normal flow if booking check fails

                await ctx.update_state("appointment_time")

                # Get available time slots
                time_slots = await get_available_time_slots(selected_doctor, ctx.hospital_id) # Pass hospital_id
                slot_options = await format_time_slots(time_slots, ctx.language) # Use ctx.language

                prompt_text = await get_translated_text(
                    f"Please select an appointment time with Dr. {selected_doctor['name']}.",
                    ctx.language # Use ctx.language
                )

                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather",
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{prompt_text} {slot_options}"
                    }
                }]
            else:
                # Doctor not found, retry
                retry_text = await get_translated_text(
                    "Sorry, I couldn't find that doctor. Please try again.",
                    ctx.language # Use ctx.language
                )

                doctor_options = await format_doctor_options(doctors, ctx.language) # Use ctx.language
                
                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather", # Remain in doctor_booking state
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{retry_text} {doctor_options}"
                    }
                }]

        elif state == "booking_limit_choice":
            # Handle choice when booking limit is reached
            # User can choose different doctor or book on next available date
            choice = determine_booking_limit_choice(speech_result, dtmf_digits)

            if choice == 1:  # Choose different doctor
                await ctx.update_state("doctor_booking")

                # Get available doctors again
                from datetime import datetime
                today = datetime.now().strftime('%Y-%m-%d')
                doctors = await get_available_doctors(ctx.hospital_id, today)
                doctor_options = await format_doctor_options(doctors, ctx.language)

                prompt_text = await get_translated_text(
                    "Please select a different doctor by saying their name or specialty, or press the corresponding number.",
                    ctx.language
                )

                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather",
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{prompt_text} {doctor_options}"
                    }
                }]

            elif choice == 2:  # Book on next available date
                next_date = ctx.data.get("next_available_date")
                if next_date:
                    await ctx.update_data({"appointment_date": next_date})
                    await ctx.update_state("appointment_time")

                    # Get available time slots for the next available date
                    doctor_data = {"id": ctx.data.get("doctor_id"), "schedule": {}}
                    time_slots = await get_available_time_slots(doctor_data, ctx.hospital_id)
                    slot_options = await format_time_slots(time_slots, ctx.language)

                    # Format the date for display
                    from datetime import datetime
                    next_date_obj = datetime.strptime(next_date, '%Y-%m-%d')
                    formatted_date = next_date_obj.strftime('%B %d, %Y')

                    prompt_text = await get_translated_text(
                        f"Please select an appointment time with Dr. {ctx.data.get('doctor_name')} on {formatted_date}.",
                        ctx.language
                    )

                    return [{
                        "verb": "gather",
                        "actionHook": "/webhook/gather",
                        "input": ["speech", "dtmf"],
                        "timeout": 15,
                        "bargein": True,
                        "say": {
                            "text": f"{prompt_text} {slot_options}"
                        }
                    }]
                else:
                    # No next available date stored, go back to doctor selection
                    await ctx.update_state("doctor_booking")
                    error_text = await get_translated_text(
                        "Sorry, there was an error. Please select a different doctor.",
                        ctx.language
                    )

                    from datetime import datetime
                    today = datetime.now().strftime('%Y-%m-%d')
                    doctors = await get_available_doctors(ctx.hospital_id, today)
                    doctor_options = await format_doctor_options(doctors, ctx.language)

                    return [{
                        "verb": "gather",
                        "actionHook": "/webhook/gather",
                        "input": ["speech", "dtmf"],
                        "timeout": 15,
                        "bargein": True,
                        "say": {
                            "text": f"{error_text} {doctor_options}"
                        }
                    }]
            else:
                # Invalid choice, retry
                retry_text = await get_translated_text(
                    "Sorry, I didn't understand. Please say 1 to choose a different doctor or 2 to book on the next available date.",
                    ctx.language
                )

                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather",
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": retry_text
                    }
                }]

        elif state == "test_booking":
            # Process test selection
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')
            tests = await get_available_tests(ctx.hospital_id, today) # Use ctx.hospital_id with date
            selected_test = match_test_selection(speech_result, dtmf_digits, tests)
            
            if selected_test:
                await ctx.update_data({
                    "test_id": selected_test["id"],
                    "test_name": selected_test["name"]
                })
                await ctx.update_state("test_time")
                
                # Get available time slots for tests
                time_slots = await get_available_test_slots(ctx.hospital_id, selected_test["id"]) # Pass hospital_id and test_id
                slot_options = await format_time_slots(time_slots, ctx.language) # Use ctx.language

                prompt_text = await get_translated_text(
                    f"Please select a time for your {selected_test['name']} test.",
                    ctx.language # Use ctx.language
                )
                
                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather",
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{prompt_text} {slot_options}"
                    }
                }]
            else:
                # Test not found, retry
                retry_text = await get_translated_text(
                    "Sorry, I couldn't find that test. Please try again.",
                    ctx.language # Use ctx.language
                )

                test_options = await format_test_options(tests, ctx.language) # Use ctx.language
                
                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather", # Remain in test_booking state
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{retry_text} {test_options}"
                    }
                }]
        
        elif state == "appointment_time" or state == "test_time":
            # Process time selection
            is_test = (state == "test_time")
            doctor_id_for_slots = ctx.data.get("doctor_id") if not is_test else None
            test_id_for_slots = ctx.data.get("test_id") if is_test else None

            time_slots = await get_available_test_slots(ctx.hospital_id, test_id_for_slots) if is_test else await get_available_time_slots({"id": doctor_id_for_slots, "schedule": {}}, ctx.hospital_id)
            
            selected_time = match_time_selection(speech_result, dtmf_digits, time_slots)
            
            if selected_time:
                await ctx.update_data({"time": selected_time})
                await ctx.update_state("patient_name")
                
                prompt_text = await get_translated_text(
                    "Please say your full name.",
                    ctx.language # Use ctx.language
                )
                
                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather",
                    "input": ["speech"], # Only speech for name
                    "timeout": 10,
                    "bargein": True,
                    "say": {
                        "text": prompt_text
                    }
                }]
            else:
                # Invalid time, retry
                retry_text = await get_translated_text(
                    "Sorry, I couldn't understand the time you selected. Please try again.",
                    ctx.language # Use ctx.language
                )

                slot_options = await format_time_slots(time_slots, ctx.language) # Use ctx.language
                
                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather", # Remain in current time selection state
                    "input": ["speech", "dtmf"],
                    "timeout": 15,
                    "bargein": True,
                    "say": {
                        "text": f"{retry_text} {slot_options}"
                    }
                }]
        
        elif state == "patient_name":
            # Process patient name
            if speech_result:
                await ctx.update_data({"patient_name": speech_result})
                await ctx.update_state("confirmation")
                
                # Format appointment details for confirmation
                is_test = "test_id" in ctx.data # Check in ctx.data
                
                if is_test:
                    details_text = await get_translated_text(
                        f"You are booking a {ctx.data.get('test_name', 'your test')} test for {ctx.data.get('time', 'the scheduled time')}.",
                        ctx.language
                    )
                else:
                    details_text = await get_translated_text(
                        f"You are booking an appointment with Dr. {ctx.data.get('doctor_name', 'your doctor')} for {ctx.data.get('time', 'the scheduled time')}.",
                        ctx.language
                    )

                confirmation_text = await get_translated_text(
                    f"Please confirm the following details: {details_text} Your name is {ctx.data.get('patient_name', 'Patient')}. "
                    f"Say yes or press 1 to confirm, or no or press 2 to cancel.",
                    ctx.language # Use ctx.language
                )
                
                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather",
                    "input": ["speech", "dtmf"],
                    "timeout": 10,
                    "bargein": True,
                    "say": {
                        "text": confirmation_text
                    }
                }]
            else:
                # No name provided, retry
                retry_text = await get_translated_text(
                    "I didn't catch your name. Please say your full name clearly.",
                    ctx.language # Use ctx.language
                )
                
                return [{
                    "verb": "gather",
                    "actionHook": "/webhook/gather", # Remain in patient_name state
                    "input": ["speech"],
                    "timeout": 10,
                    "bargein": True,
                    "say": {
                        "text": retry_text
                    }
                }]
        
        elif state == "confirmation":
            # Process confirmation
            confirmed = is_confirmation_positive(speech_result, dtmf_digits)
            
            if confirmed:
                # Save the appointment/test booking
                is_test = "test_id" in ctx.data # Check in ctx.data
                
                try:
                    if is_test:
                        # Handle test booking time properly using natural language parser
                        test_time = ctx.data.get("time")
                        test_date = ctx.data.get("appointment_date")  # Tests can also have specific dates

                        # Use the new natural language time parser to avoid invalid timestamps
                        from .utils import format_appointment_datetime
                        full_test_time = format_appointment_datetime(test_time, test_date)

                        if not full_test_time:
                            # Fallback error handling if parsing fails
                            error_text = await get_translated_text(
                                "There was an error processing your test appointment time. Please try again.",
                                ctx.language
                            )
                            return [
                                {"verb": "say", "text": error_text},
                                {"verb": "hangup"}
                            ]

                        await save_test_booking(
                            hospital_id=ctx.hospital_id,
                            patient_name=ctx.data.get("patient_name"),
                            phone=ctx.caller_number,
                            test_type_id=ctx.data.get("test_id"),
                            time=full_test_time
                        )
                    else:
                        # Handle appointment date and time properly using natural language parser
                        appointment_time = ctx.data.get("time")
                        appointment_date = ctx.data.get("appointment_date")

                        # Use the new natural language time parser to avoid invalid timestamps
                        from .utils import format_appointment_datetime
                        full_appointment_time = format_appointment_datetime(appointment_time, appointment_date)

                        if not full_appointment_time:
                            # Fallback error handling if parsing fails
                            error_text = await get_translated_text(
                                "There was an error processing your appointment time. Please try again.",
                                ctx.language
                            )
                            return [
                                {"verb": "say", "text": error_text},
                                {"verb": "hangup"}
                            ]

                        await save_appointment(
                            hospital_id=ctx.hospital_id,
                            patient_name=ctx.data.get("patient_name"),
                            phone=ctx.caller_number,
                            doctor_id=ctx.data.get("doctor_id"),
                            time=full_appointment_time
                        )
                    
                    # Send SMS confirmation
                    await send_confirmation_sms(
                        phone=ctx.caller_number,
                        appointment_type="test" if is_test else "appointment",
                        details=ctx.data, # Pass ctx.data
                        language=ctx.language,
                        hospital_id=ctx.hospital_id
                    )
                    
                    success_text = await get_translated_text(
                        f"Your {'test' if is_test else 'appointment'} has been booked successfully. You will receive a confirmation SMS shortly. Thank you for calling.",
                        ctx.language # Use ctx.language
                    )
                    
                    return [
                        {"verb": "say", "text": success_text},
                        {"verb": "hangup"}
                    ]
                except Exception as e:
                    logger.error(f"Error saving booking: {e}")
                    error_text = await get_translated_text(
                        "We encountered an error while booking. Please try again or call back later.",
                        ctx.language # Use ctx.language
                    )
                    return [
                        {"verb": "say", "text": error_text},
                        {"verb": "hangup"}
                    ]
            else:
                # Booking cancelled
                cancel_text = await get_translated_text(
                    "Your booking has been cancelled. Thank you for calling.",
                    ctx.language # Use ctx.language
                )
                
                return [
                    {"verb": "say", "text": cancel_text},
                    {"verb": "hangup"}
                ]
        
        # Default fallback response if state is not matched or other issue
        fallback_text = await get_translated_text(
            "I'm sorry, I didn't understand that. Please try again.",
            ctx.language if ctx else "en" # Use ctx.language or default
        )
        
        return [{
            "verb": "gather",
            "actionHook": "/webhook/gather", # Default action, try to re-process in current state
            "input": ["speech", "dtmf"],
            "timeout": 10,
            "bargein": True,
            "say": {
                "text": fallback_text
            }
        }]
        
    except Exception as e:
        logger.error(f"Error handling gather: {e}")
        return [
            {"verb": "say", "text": language_config.get_error_message(
                error_type="system_error",
                language=get_primary_language()  # Default to Hindi since we don't have language context
            )},
            {"verb": "hangup"}
        ]

@app.post("/webhook/emergency")
async def handle_emergency(request: Request):
    """
    Handle emergency call routing (Jambonz only)
    """
    try:
        try:
            payload = await request.json()
        except Exception as json_err:
            import json
            if isinstance(json_err, json.JSONDecodeError):
                logger.error(f"Invalid JSON in emergency webhook: {json_err}")
                return [
                    {"verb": "say", "text": "Invalid request format. Please send valid JSON."},
                    {"verb": "hangup"}
                ]
            else:
                logger.error(f"Unexpected error parsing JSON in emergency webhook: {json_err}")
                return [
                    {"verb": "say", "text": language_config.get_error_message(
                        error_type="system_error",
                        language=get_primary_language()  # Default to Hindi since we don't have language context
                    )},
                    {"verb": "hangup"}
                ]

        logger.info(f"Received emergency webhook: {payload}")
        call_id = payload.get('call_id')
        if call_id:
            ctx = await CallContext.get_or_create(call_id=call_id)
            if ctx:
                await ctx.update_state('emergency_handled')

        # For this demo, we will just provide an information message and hangup.
        # In production, this might involve a 'dial' verb to an emergency number or SIP URI.
        emergency_message = "This is a simulated emergency response. In a real system, we would connect you to emergency services or a doctor immediately. Thank you for testing."

        return [
            {"verb": "say", "text": emergency_message},
            {"verb": "hangup"}
        ]

    except Exception as e:
        logger.error(f"Error handling emergency: {e}")
        return [
            {"verb": "say", "text": language_config.get_error_message(
                error_type="system_error",
                language=get_primary_language()  # Default to Hindi since we don't have language context
            )},
            {"verb": "hangup"}
        ]

@app.post("/api/semantic-query")
async def semantic_query_endpoint(request: Request):
    """
    API endpoint for testing semantic query processing.
    Useful for debugging and performance monitoring.
    """
    try:
        payload = await request.json()
        query = payload.get("query", "")
        hospital_id = payload.get("hospital_id", "456")
        language = payload.get("language", "en")

        if not query:
            return {"error": "Query is required"}

        # Process the query using semantic engine
        result = await process_voice_query(query, hospital_id, language)

        # Add performance metrics
        metrics = await semantic_engine.get_performance_metrics()

        return {
            "query": query,
            "hospital_id": hospital_id,
            "language": language,
            "result": result,
            "performance_metrics": metrics
        }

    except Exception as e:
        logger.error(f"Error in semantic query endpoint: {e}")
        return {"error": str(e)}

@app.get("/api/performance-metrics")
async def get_performance_metrics():
    """
    Get performance metrics for the semantic engine.
    """
    try:
        metrics = await semantic_engine.get_performance_metrics()
        return metrics
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        return {"error": str(e)}

@app.post("/api/preload-hospital/{hospital_id}")
async def preload_hospital_cache_endpoint(hospital_id: str, request: Request):
    """
    Preload semantic cache for a specific hospital.
    """
    try:
        hospital_data = await request.json()
        success = await semantic_engine.preload_hospital_cache(hospital_id, hospital_data)

        return {
            "hospital_id": hospital_id,
            "success": success,
            "message": "Cache preloaded successfully" if success else "Failed to preload cache"
        }

    except Exception as e:
        logger.error(f"Error preloading hospital cache: {e}")
        return {"error": str(e)}

@app.post("/api/booking-limits/refresh")
async def manual_booking_limit_refresh(request: Request):
    """
    Manually trigger booking limit refresh for testing or immediate updates.
    """
    try:
        payload = await request.json()
        hospital_id = payload.get("hospital_id")  # Optional: specific hospital

        from .booking_limit_scheduler import booking_scheduler
        result = await booking_scheduler.manual_refresh(hospital_id)

        return result

    except Exception as e:
        logger.error(f"Error in manual booking limit refresh: {e}")
        return {"success": False, "error": str(e)}

@app.get("/api/booking-limits/status/{hospital_id}/{doctor_id}")
async def get_booking_limit_status(hospital_id: str, doctor_id: str, date: str = None):
    """
    Get current booking limit status for a doctor.
    """
    try:
        from datetime import datetime
        if not date:
            date = datetime.now().strftime('%Y-%m-%d')

        from .cache_manager import redis_manager
        availability = await redis_manager.check_booking_availability_redis_async(
            hospital_id, doctor_id, date
        )

        return {
            "hospital_id": hospital_id,
            "doctor_id": doctor_id,
            "date": date,
            "availability": availability,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting booking limit status: {e}")
        return {"error": str(e)}

@app.post("/api/availability/refresh")
async def manual_availability_refresh(request: Request):
    """
    Manually trigger availability refresh for testing or immediate updates.
    """
    try:
        payload = await request.json()
        hospital_id = payload.get("hospital_id")  # Optional: specific hospital
        date = payload.get("date")  # Optional: specific date

        logger.info(f"Manual availability refresh requested for hospital: {hospital_id or 'all'}, date: {date or 'next 7 days'}")

        from .booking_limit_scheduler import booking_scheduler

        if hospital_id:
            # Refresh specific hospital
            doctors_refreshed = await booking_scheduler._refresh_hospital_booking_limits(hospital_id)
            await booking_scheduler._refresh_hospital_test_availability(hospital_id)

            return {
                "success": True,
                "hospital_id": hospital_id,
                "doctors_refreshed": doctors_refreshed,
                "message": f"Refreshed availability for {doctors_refreshed} doctors and all tests"
            }
        else:
            # Refresh all hospitals
            await booking_scheduler._refresh_all_booking_limits()
            return {
                "success": True,
                "message": "Manual availability refresh completed for all hospitals"
            }

    except Exception as e:
        logger.error(f"Error in manual availability refresh: {e}")
        return {"success": False, "error": str(e)}

def extract_hospital_id_from_did(did: str) -> Optional[str]:
    """
    Extracts the hospital ID from the DID (phone number).
    The hospital ID is assumed to be the last 10 digits of the numeric part of the DID.
    """
    logger.info(f"Attempting to extract hospital ID from DID: {did}")

    if not did:
        logger.warning("DID is None or empty, cannot extract hospital ID.")
        return None

    # Clean the DID to get only numeric characters
    cleaned_did = ''.join(filter(str.isdigit, did))
    logger.info(f"Cleaned DID for hospital ID extraction: {cleaned_did}")

    # Check if the cleaned DID has at least 10 digits
    if len(cleaned_did) >= 10:
        # Take the last 10 digits as the hospital_id
        hospital_id = cleaned_did[-10:]
        logger.info(f"Extracted 10-digit hospital ID: {hospital_id} from DID: {did} (cleaned: {cleaned_did})")
        return hospital_id
    else:
        logger.warning(
            f"Cleaned DID '{cleaned_did}' (from original DID '{did}') is too short "
            f"to extract a 10-digit hospital ID. A minimum of 10 digits is required."
        )
        return None

# Note: get_speech_code and get_language_name are imported from language_config
# No need to redefine them locally as they are already available

async def get_translated_text(text: str, language: str) -> str:
    """
    Get translated text for the selected language
    Fetches translations from Firebase if available, otherwise uses Google Translate API
    """
    if language == 'en':
        # No need to translate if the language is English
        return text
        
    try:
        # Try to fetch translation from Firebase first
        db = get_firestore_db()
        loop = asyncio.get_running_loop()
        # Off-load the blocking Firestore call to the default executor
        translations_ref = await loop.run_in_executor(None, db.collection('translations').document(language).get)
        
        if translations_ref.exists:
            translations = translations_ref.to_dict()
            # Check if this specific text has a translation
            if text in translations:
                return translations[text]
        
        # If we reach here, no translation was found in Firebase
        # Use Google Translate API instead (or your preferred translation service)
        # This section would integrate with a translation API
        # For now, we'll use a fallback with some common translations
    
    except Exception as e:
        logger.error(f"Error in translation: {e}")
    
    # If all translation attempts fail, return original text
    return text

def determine_menu_selection(speech: str, dtmf: str) -> int:
    """
    Determine menu selection from speech or DTMF input
    """
    if dtmf == '1':
        return 1
    elif dtmf == '2':
        return 2

    speech = speech.lower()
    if '1' in speech or 'one' in speech or 'doctor' in speech or 'appointment' in speech:
        return 1
    elif '2' in speech or 'two' in speech or 'test' in speech:
        return 2

    # Default to 0 for invalid selection
    return 0

def determine_booking_limit_choice(speech: str, dtmf: str) -> int:
    """
    Determine choice when booking limit is reached:
    1 = Choose different doctor
    2 = Book on next available date
    """
    if dtmf == '1':
        return 1
    elif dtmf == '2':
        return 2

    speech = speech.lower()
    if '1' in speech or 'one' in speech or 'different' in speech or 'other' in speech or 'another' in speech:
        return 1
    elif '2' in speech or 'two' in speech or 'next' in speech or 'available' in speech or 'date' in speech:
        return 2

    # Default to 0 for invalid selection
    return 0

async def get_available_doctors(hospital_id: str, date: str = None) -> list:
    """
    Get available doctors from Firestore, filtered by availability if date is provided
    """
    logger.info(f"Getting available doctors for hospital ID: {hospital_id}, date: {date}")

    # If no date provided, use today's date
    if not date:
        from datetime import datetime
        date = datetime.now().strftime('%Y-%m-%d')

    db = get_firestore_db()
    doctors = []
    
    # Based on the init_data.py script and Firebase screenshot, we know doctors are stored in:
    # hospital_456_data/doctors/doctors collection
    try:
        # First try the path from the data initialization script
        main_data_collection = f'hospital_{hospital_id}_data'
        doctors_ref = db.collection(main_data_collection).document('doctors').collection('doctors')
        logger.info(f"Looking for doctors at {doctors_ref.path}")
        
        loop = asyncio.get_running_loop()
        # Off-load the blocking Firestore call to the default executor
        docs = await loop.run_in_executor(None, doctors_ref.get)
        doc_count = 0
        
        for i, doc in enumerate(docs):
            doc_count += 1
            doctor_data = doc.to_dict()
            doctor_data['id'] = doc.id
            doctor_data['number'] = i + 1  # Assign a number for DTMF selection
            doctors.append(doctor_data)
        
        logger.info(f"Found {doc_count} doctors in {doctors_ref.path}")
    except Exception as e:
        logger.error(f"Error getting doctors from primary path: {e}")
        
        try:
            # Alternative path: also use hospital_XXX_data structure
            main_data_collection_alt = f'hospital_{hospital_id}_data'
            doctors_ref_alt = db.collection(main_data_collection_alt).document('doctors').collection('doctors')
            logger.info(f"Looking for doctors at alternative path: {doctors_ref_alt.path}")
            
            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            docs = await loop.run_in_executor(None, doctors_ref_alt.get)
            doc_count = 0
            
            for i, doc in enumerate(docs):
                doc_count += 1
                doctor_data = doc.to_dict()
                doctor_data['id'] = doc.id
                doctor_data['number'] = i + 1  # Assign a number for DTMF selection
                doctors.append(doctor_data)
            
            logger.info(f"Found {doc_count} doctors in alternative path {doctors_ref_alt.path}")
        except Exception as alt_e:
            logger.error(f"Error getting doctors from alternative path: {alt_e}")
    
    # If no data was found, log a warning but don't use dummy data
    if not doctors:
        logger.warning(f"No doctors found for hospital {hospital_id} in any collection path. Please check your Firebase data structure.")
        # Return empty list instead of dummy data
        return []

    # Filter doctors by availability if date is provided
    if date:
        try:
            from .cache_manager import redis_manager
            available_doctor_ids = await redis_manager.get_available_items_async(hospital_id, 'doctor', date)

            if available_doctor_ids:
                # Filter doctors to only include available ones
                available_doctors = []
                for doctor in doctors:
                    doctor_id = doctor.get('id')
                    # Check if doctor is in available list or if no availability data exists (default to available)
                    if doctor_id in available_doctor_ids or not available_doctor_ids:
                        available_doctors.append(doctor)
                    else:
                        # Check Firebase for availability data as fallback
                        daily_availability = doctor.get('daily_availability', {})
                        is_available = daily_availability.get(date, True)  # Default to available
                        if is_available:
                            available_doctors.append(doctor)

                logger.info(f"Filtered {len(doctors)} doctors to {len(available_doctors)} available on {date}")
                return available_doctors
            else:
                # No Redis data, check Firebase availability
                available_doctors = []
                for doctor in doctors:
                    daily_availability = doctor.get('daily_availability', {})
                    is_available = daily_availability.get(date, True)  # Default to available
                    if is_available:
                        available_doctors.append(doctor)

                logger.info(f"Using Firebase availability: {len(available_doctors)} doctors available on {date}")
                return available_doctors
        except Exception as e:
            logger.error(f"Error filtering doctors by availability: {e}")
            # Return all doctors if filtering fails

    return doctors

async def get_available_tests(hospital_id: str, date: str = None) -> list:
    """
    Get available tests from Firestore, filtered by availability if date is provided
    """
    logger.info(f"Getting available tests for hospital ID: {hospital_id}, date: {date}")

    # If no date provided, use today's date
    if not date:
        from datetime import datetime
        date = datetime.now().strftime('%Y-%m-%d')

    db = get_firestore_db()
    tests = []
    
    # Based on the init_data.py script and screenshots, the tests are stored in:
    # hospital_456_data/test_info/tests collection
    try:
        # First try the path from the data initialization script
        main_data_collection = f'hospital_{hospital_id}_data'
        test_info_doc_ref = db.collection(main_data_collection).document('test_info')
        
        # First check if there's a tests subcollection
        tests_ref = test_info_doc_ref.collection('tests')
        logger.info(f"Looking for tests at {tests_ref.path}")
        
        loop = asyncio.get_running_loop()
        # Off-load the blocking Firestore call to the default executor
        docs = await loop.run_in_executor(None, tests_ref.get)
        test_count = 0
        
        for i, doc in enumerate(docs):
            test_count += 1
            test_data = doc.to_dict()
            test_data['id'] = doc.id
            test_data['number'] = i + 1  # Assign a number for DTMF selection
            tests.append(test_data)
        
        logger.info(f"Found {test_count} tests in {tests_ref.path}")
        
        # If we don't find any tests in the subcollection, check if they're in the test_info document itself
        if test_count == 0:
            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            test_info_doc = await loop.run_in_executor(None, test_info_doc_ref.get)
            if test_info_doc.exists:
                test_info_data = test_info_doc.to_dict()
                logger.info(f"Looking for tests in test_info document: {test_info_data}")
                
                if 'tests' in test_info_data and isinstance(test_info_data['tests'], list):
                    for i, test in enumerate(test_info_data['tests']):
                        test['id'] = f"test{i+1}" if 'id' not in test else test['id']
                        test['number'] = i + 1
                        tests.append(test)
                    logger.info(f"Found {len(tests)} tests in test_info document")
    except Exception as e:
        logger.error(f"Error getting tests from primary path: {e}")
        
        try:
            # Alternative path: also use hospital_XXX_data structure
            main_data_collection_alt = f'hospital_{hospital_id}_data'
            tests_ref_alt = db.collection(main_data_collection_alt).document('test_info').collection('tests')
            logger.info(f"Looking for tests at alternative path: {tests_ref_alt.path}")
            
            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            docs = await loop.run_in_executor(None, tests_ref_alt.get)
            test_count = 0
            
            for i, doc in enumerate(docs):
                test_count += 1
                test_data = doc.to_dict()
                test_data['id'] = doc.id
                test_data['number'] = i + 1  # Assign a number for DTMF selection
                tests.append(test_data)
            
            logger.info(f"Found {test_count} tests in alternative path {tests_ref_alt.path}")
        except Exception as alt_e:
            logger.error(f"Error getting tests from alternative path: {alt_e}")
    
    # If no data was found in any collection path, use dummy data for testing
    if not tests:
        logger.warning(f"No tests found for hospital {hospital_id} in any collection path. Please check your Firebase data structure.")
        # Return empty list instead of dummy data
        return []

    # Filter tests by availability if date is provided
    if date:
        try:
            from .cache_manager import redis_manager
            available_test_ids = await redis_manager.get_available_items_async(hospital_id, 'test', date)

            if available_test_ids:
                # Filter tests to only include available ones
                available_tests = []
                for test in tests:
                    test_id = test.get('id')
                    # Check if test is in available list or if no availability data exists (default to available)
                    if test_id in available_test_ids or not available_test_ids:
                        available_tests.append(test)
                    else:
                        # Check Firebase for availability data as fallback
                        daily_availability = test.get('daily_availability', {})
                        is_available = daily_availability.get(date, True)  # Default to available
                        if is_available:
                            available_tests.append(test)

                logger.info(f"Filtered {len(tests)} tests to {len(available_tests)} available on {date}")
                return available_tests
            else:
                # No Redis data, check Firebase availability
                available_tests = []
                for test in tests:
                    daily_availability = test.get('daily_availability', {})
                    is_available = daily_availability.get(date, True)  # Default to available
                    if is_available:
                        available_tests.append(test)

                logger.info(f"Using Firebase availability: {len(available_tests)} tests available on {date}")
                return available_tests
        except Exception as e:
            logger.error(f"Error filtering tests by availability: {e}")
            # Return all tests if filtering fails

    return tests

async def format_doctor_options(doctors: list, language: str) -> str:
    """
    Format doctor options for speech output
    """
    if not doctors:
        return await get_translated_text("No doctors are currently available.", language)
    
    # Create a string with doctor options
    options = []
    for doctor in doctors:
        option = f"{doctor['number']} for {doctor['name']}, {doctor.get('specialty', '')}"
        options.append(option)
    
    return ", ".join(options)

async def format_test_options(tests: list, language: str) -> str:
    """
    Format test options for speech output
    """
    if not tests:
        return await get_translated_text("No tests are currently available.", language)
    
    # Create a string with test options
    options = []
    for test in tests:
        option = f"{test['number']} for {test['name']}"
        options.append(option)
    
    return ", ".join(options)

def match_doctor_selection(speech: str, dtmf: str, doctors: list) -> Optional[dict]:
    """
    Match user input to a doctor from the list
    """
    if not doctors:
        return None
    
    # Check DTMF input first
    if dtmf:
        try:
            selection = int(dtmf)
            for doctor in doctors:
                if doctor['number'] == selection:
                    return doctor
        except ValueError:
            pass
    
    # Check speech input
    speech = speech.lower()
    best_match = None
    best_score = 0
    
    for doctor in doctors:
        # Check for doctor name match
        name_score = fuzzy_match_score(speech, doctor['name'].lower())
        # Check for specialty match
        specialty_score = fuzzy_match_score(speech, doctor.get('specialty', '').lower())
        
        # Use the higher of the two scores
        score = max(name_score, specialty_score)
        if score > best_score and score > 0.7:  # 70% match threshold
            best_score = score
            best_match = doctor
    
    return best_match

def match_test_selection(speech: str, dtmf: str, tests: list) -> Optional[dict]:
    """
    Match user input to a test from the list
    """
    if not tests:
        return None
    
    # Check DTMF input first
    if dtmf:
        try:
            selection = int(dtmf)
            for test in tests:
                if test['number'] == selection:
                    return test
        except ValueError:
            pass
    
    # Check speech input
    speech = speech.lower()
    best_match = None
    best_score = 0
    
    for test in tests:
        score = fuzzy_match_score(speech, test['name'].lower())
        if score > best_score and score > 0.7:  # 70% match threshold
            best_score = score
            best_match = test
    
    return best_match

def fuzzy_match_score(input_text: str, target_text: str) -> float:
    """
    Calculate a similarity score between input and target text
    Uses a simple method - in a real system you would use a proper fuzzy matching algorithm
    """
    input_words = set(input_text.split())
    target_words = set(target_text.split())
    
    # If either set is empty, return 0
    if not input_words or not target_words:
        return 0
    
    # Calculate Jaccard similarity
    intersection = len(input_words.intersection(target_words))
    union = len(input_words.union(target_words))
    
    return intersection / union if union > 0 else 0

async def get_available_time_slots(doctor: dict, hospital_id: str) -> list:
    """
    Get available time slots for a doctor by checking their schedule in Firebase
    and existing appointments in PostgreSQL
    """
    if not doctor or not hospital_id:
        logger.warning("Cannot get time slots: missing doctor data or hospital ID")
        return []
    
    try:
        # 1. Get doctor's schedule from Firebase
        db = get_firestore_db()
        doctor_id = doctor.get('id')
        
        # Check schedule in hospital_XXX_data/doctors/doctors/{doctor_id}/schedule
        schedule_doc = None
        try:
            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            schedule_doc = await loop.run_in_executor(None, db.collection(f'hospital_{hospital_id}_data').document('doctors').collection('doctors').document(doctor_id).collection('schedule').document('weekly').get)
        except Exception as e:
            logger.warning(f"Error getting doctor schedule from first path: {e}")
            
        # Try alternative path if first attempt failed
        if not schedule_doc or not schedule_doc.exists:
            try:
                # Alternative path for doctor schedule, aligned with hospital_XXX_data structure
                loop = asyncio.get_running_loop()
                # Off-load the blocking Firestore call to the default executor
                schedule_doc = await loop.run_in_executor(None, db.collection(f'hospital_{hospital_id}_data').document('doctors').collection('doctors').document(doctor_id).collection('schedule').document('weekly').get)
            except Exception as e:
                logger.warning(f"Error getting doctor schedule from alternative path: {e}")
        
        # 2. Get booked appointments from PostgreSQL
        conn = None
        booked_slots = []
        try:
            # Get PostgreSQL connection for this hospital
            conn = await get_postgres_connection(hospital_id)
            if conn:
                # Query appointments for this doctor for tomorrow
                tomorrow = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
                tomorrow_end = tomorrow.replace(hour=23, minute=59, second=59)
                
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT start_time, end_time FROM appointments WHERE doctor_id = %s AND start_time BETWEEN %s AND %s AND status = 'scheduled'",
                    (doctor_id, tomorrow, tomorrow_end)
                )
                booked_slots = cursor.fetchall()
                cursor.close()
        except Exception as e:
            logger.error(f"Error fetching booked appointments: {e}")
        finally:
            if conn and hasattr(conn, 'close'):
                conn.close()
        
        # 3. Determine available slots based on schedule and booked appointments
        # Default working hours if no schedule found
        working_hours = {
            "start": "09:00", 
            "end": "17:00"
        }
        
        # Extract actual working hours from doctor's schedule if available
        if schedule_doc and schedule_doc.exists:
            schedule_data = schedule_doc.to_dict()
            tomorrow_day = (datetime.now() + timedelta(days=1)).strftime("%A").lower()
            if tomorrow_day in schedule_data and schedule_data[tomorrow_day].get("available", False):
                day_schedule = schedule_data[tomorrow_day]
                working_hours = {
                    "start": day_schedule.get("start_time", "09:00"),
                    "end": day_schedule.get("end_time", "17:00")
                }
            elif "default" in schedule_data:
                default_schedule = schedule_data["default"]
                working_hours = {
                    "start": default_schedule.get("start_time", "09:00"),
                    "end": default_schedule.get("end_time", "17:00")
                }
        
        # Generate potential slots based on working hours
        start_hour = int(working_hours["start"].split(":")[0])
        end_hour = int(working_hours["end"].split(":")[0])
        
        # Generate hourly slots
        all_slots = []
        for hour in range(start_hour, end_hour):
            period = "AM" if hour < 12 else "PM"
            display_hour = hour if hour <= 12 else hour - 12
            if display_hour == 0:
                display_hour = 12
            slot = f"{display_hour} {period} tomorrow"
            all_slots.append(slot)
        
        # Remove booked slots (simplified approach)
        # In a real implementation, you would convert the slot strings to datetime objects for comparison
        available_slots = all_slots
        
        # If no slots available, provide a helpful message
        if not available_slots:
            return ["No slots available tomorrow"]
            
        return available_slots
        
    except Exception as e:
        logger.error(f"Error getting available time slots: {e}")
        # Return empty list instead of using hardcoded fallbacks
        return []

async def get_available_test_slots(hospital_id: str, test_id: str = None) -> list:
    """
    Get available time slots for tests by checking test schedule in Firebase
    and existing test bookings in PostgreSQL
    """
    if not hospital_id:
        logger.warning("Cannot get test slots: missing hospital ID")
        return []
    
    try:
        # 1. Get test schedule from Firebase
        db = get_firestore_db()
        test_schedule = None
        
        try:
            # Try to get test schedule from test_info document
            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            test_info_doc = await loop.run_in_executor(None, db.collection(f'hospital_{hospital_id}_data').document('test_info').get)
            if test_info_doc.exists:
                test_info = test_info_doc.to_dict()
                test_schedule = test_info.get('schedule', {})
        except Exception as e:
            logger.warning(f"Error getting test schedule from first path: {e}")
        
        # Try alternative path if first attempt failed
        if not test_schedule:
            try:
                # Alternative path for test schedule, aligned with hospital_XXX_data structure
                loop = asyncio.get_running_loop()
                # Off-load the blocking Firestore call to the default executor
                test_info_doc_alt = await loop.run_in_executor(None, db.collection(f'hospital_{hospital_id}_data').document('test_info').get)
                if test_info_doc_alt.exists:
                    test_info_alt = test_info_doc_alt.to_dict()
                    test_schedule = test_info_alt.get('schedule', {}) # Access schedule field as in primary path
            except Exception as e:
                logger.warning(f"Error getting test schedule from alternative path: {e}")
        
        # 2. Get booked test slots from PostgreSQL
        conn = None
        booked_slots = []
        try:
            # Get PostgreSQL connection for this hospital
            conn = await get_postgres_connection(hospital_id)
            if conn:
                # Query test bookings for tomorrow
                tomorrow = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
                tomorrow_end = tomorrow.replace(hour=23, minute=59, second=59)
                
                cursor = conn.cursor()
                query = "SELECT booking_time FROM test_bookings WHERE hospital_id = %s AND booking_time BETWEEN %s AND %s AND status = 'scheduled'"
                params = [hospital_id, tomorrow, tomorrow_end]
                
                # Add test_id filter if provided
                if test_id:
                    query += " AND test_type_id = %s"
                    params.append(test_id)
                    
                cursor.execute(query, params)
                booked_slots = cursor.fetchall()
                cursor.close()
        except Exception as e:
            logger.error(f"Error fetching booked test slots: {e}")
        finally:
            if conn and hasattr(conn, 'close'):
                conn.close()
        
        # 3. Determine available slots based on schedule and booked slots
        # Default testing hours
        testing_hours = {
            "start": "09:00", 
            "end": "17:00"
        }
        
        # Extract actual testing hours from test schedule if available
        if test_schedule:
            tomorrow_day = (datetime.now() + timedelta(days=1)).strftime("%A").lower()
            if tomorrow_day in test_schedule and test_schedule[tomorrow_day].get("available", False):
                day_schedule = test_schedule[tomorrow_day]
                testing_hours = {
                    "start": day_schedule.get("start_time", "09:00"),
                    "end": day_schedule.get("end_time", "17:00")
                }
            elif "default" in test_schedule:
                default_schedule = test_schedule["default"]
                testing_hours = {
                    "start": default_schedule.get("start_time", "09:00"),
                    "end": default_schedule.get("end_time", "17:00")
                }
        
        # Generate potential slots based on testing hours
        start_hour = int(testing_hours["start"].split(":")[0])
        end_hour = int(testing_hours["end"].split(":")[0])
        
        # Generate hourly slots
        all_slots = []
        for hour in range(start_hour, end_hour):
            period = "AM" if hour < 12 else "PM"
            display_hour = hour if hour <= 12 else hour - 12
            if display_hour == 0:
                display_hour = 12
            slot = f"{display_hour} {period} tomorrow"
            all_slots.append(slot)
        
        # Remove booked slots (simplified approach)
        # In a real implementation, you would convert the slot strings to datetime objects for comparison
        available_slots = all_slots
        
        # If no slots available, provide a helpful message
        if not available_slots:
            return ["No slots available tomorrow"]
            
        return available_slots
        
    except Exception as e:
        logger.error(f"Error getting available test slots: {e}")
        # Return empty list instead of using hardcoded fallbacks
        return []

async def format_time_slots(slots: list, language: str) -> str:
    """
    Format time slots for speech output
    """
    if not slots:
        return await get_translated_text("No time slots are currently available.", language)
    
    # Create a string with time slot options
    options = []
    for i, slot in enumerate(slots):
        option = f"{i+1} for {slot}"
        options.append(option)
    
    return ", ".join(options)

def match_time_selection(speech: str, dtmf: str, time_slots: list) -> Optional[str]:
    """
    Match user input to a time slot from the list
    """
    if not time_slots:
        return None
    
    # Check DTMF input first
    if dtmf:
        try:
            selection = int(dtmf)
            if 1 <= selection <= len(time_slots):
                return time_slots[selection - 1]
        except ValueError:
            pass
    
    # Check speech input
    speech = speech.lower()
    for slot in time_slots:
        if slot.lower() in speech:
            return slot
    
    # If no exact match, try fuzzy matching
    best_match = None
    best_score = 0
    
    for slot in time_slots:
        score = fuzzy_match_score(speech, slot.lower())
        if score > best_score and score > 0.7:  # 70% match threshold
            best_score = score
            best_match = slot
    
    return best_match

def is_confirmation_positive(speech: str, dtmf: str) -> bool:
    """
    Check if the user confirmed the appointment
    """
    if dtmf == '1':
        return True
    elif dtmf == '2':
        return False
    
    speech = speech.lower()
    positive_responses = ['yes', 'correct', 'right', 'confirm', 'book', 'ok', 'okay']
    negative_responses = ['no', 'not', 'wrong', 'cancel', 'don\'t']
    
    for response in positive_responses:
        if response in speech:
            return True
    
    for response in negative_responses:
        if response in speech:
            return False
    
    # Default to False if confirmation is unclear
    return False

async def save_appointment(hospital_id: str, patient_name: str, phone: str, doctor_id: str, time: str):
    """
    Save appointment to PostgreSQL and update booking counters in both Firebase and Redis
    """
    try:
        # Use the create_appointment function from database.py which handles Firebase counter increment
        from .database import create_appointment
        appointment_id = await create_appointment(hospital_id, patient_name, phone, doctor_id, time)

        # Also increment Redis counter for real-time tracking
        from datetime import datetime
        if isinstance(time, str) and ' ' in time:
            date_part = time.split(' ')[0]
        else:
            date_part = datetime.now().strftime('%Y-%m-%d')

        from .cache_manager import redis_manager
        new_count = await redis_manager.increment_booking_counter_async(hospital_id, doctor_id, date_part)

        if new_count > 0:
            logger.info(f"Updated Redis booking counter for doctor {doctor_id} on {date_part}: {new_count}")

        logger.info(f"Successfully created appointment {appointment_id} for patient {patient_name}")
        return appointment_id
    except Exception as e:
        logger.error(f"Error saving appointment: {e}")
        raise

async def save_test_booking(hospital_id: str, patient_name: str, phone: str, test_type_id: str, time: str):
    """
    Save test booking to PostgreSQL
    """
    # Get PostgreSQL connection for the hospital
    conn = await get_postgres_connection(hospital_id)
    
    try:
        cursor = conn.cursor()
        
        # Insert test booking
        query = """
        INSERT INTO test_bookings (patient_name, phone, test_type_id, "time", status, created_at)
        VALUES (%s, %s, %s, %s, 'scheduled', CURRENT_DATE)
        """
        cursor.execute(query, (patient_name, phone, test_type_id, time))
        
        conn.commit()
        cursor.close()
        conn.close()
    except Exception as e:
        logger.error(f"Error saving test booking: {e}")
        if conn:
            conn.close()
        raise

async def send_confirmation_sms(phone: str, appointment_type: str, details: dict, language: str, hospital_id: str):
    """
    Send confirmation SMS to the patient using Jambonz service.
    """
    logger.info(f"Attempting to send confirmation SMS to {phone} for {appointment_type} for hospital {hospital_id}")

    try:
        # Fetch hospital_config to get hospital_name
        hospital_config = await get_hospital_config(hospital_id)
        hospital_name = hospital_config.name if hospital_config else "our facility"

        patient_name = details.get("patient_name", "Patient")
        appointment_time = details.get("time", "the scheduled time")

        if appointment_type == "test":
            test_name = details.get("test_name", "your test")
            sms_body = f"Hello {patient_name}, your {test_name} at {hospital_name} has been confirmed for {appointment_time}. Reply HELP for assistance."
        else: # doctor appointment
            doctor_name = details.get("doctor_name", "your doctor")
            sms_body = f"Hello {patient_name}, your appointment with Dr. {doctor_name} at {hospital_name} has been confirmed for {appointment_time}. Please arrive 15 minutes early. Reply HELP for assistance."

        # Determine the from_number (placeholder for now)
        from_number_placeholder = "+10000000000" # Or fetch from hospital_config if available later
        logger.info(f"Using placeholder from_number: {from_number_placeholder} for SMS to {phone}")

        # Call jambonz_service.send_sms
        sms_result = jambonz_service.send_sms(
            to_number=phone,
            from_number=from_number_placeholder,
            message=sms_body,
            hospital_id=hospital_id
        )

        if sms_result.get("success"):
            logger.info(f"Confirmation SMS dispatch attempted successfully for {phone}: {sms_result.get('message')}")
        else:
            logger.error(f"Failed to dispatch confirmation SMS for {phone}: {sms_result.get('message')}")

    except Exception as e:
        logger.error(f"Error in send_confirmation_sms for {phone}, hospital {hospital_id}: {str(e)}")
        # Optionally, re-raise or handle more gracefully depending on desired behavior

@app.get("/health")
async def health_check():
    """Health check endpoint with semantic cache status"""
    try:
        # Get semantic cache performance metrics
        metrics = await semantic_engine.get_performance_metrics()

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "semantic_cache": {
                "total_queries": metrics.get("total_queries", 0),
                "cache_hit_rate_percent": metrics.get("cache_hit_rate_percent", 0),
                "avg_response_time_ms": metrics.get("avg_response_time_ms", 0)
            }
        }
    except Exception as e:
        logger.error(f"Error getting health check metrics: {e}")
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "semantic_cache": {"status": "metrics_unavailable"}
        }

@app.get("/semantic-cache/status")
async def semantic_cache_status():
    """Get detailed semantic cache status and statistics"""
    try:
        metrics = await semantic_engine.get_performance_metrics()
        return {
            "status": "active",
            "metrics": metrics,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting semantic cache status: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

if __name__ == "__main__":
    import uvicorn
    
    # Run the FastAPI app
    uvicorn.run(app, host="0.0.0.0", port=8000)
