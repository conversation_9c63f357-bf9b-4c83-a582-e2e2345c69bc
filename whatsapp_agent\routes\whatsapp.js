/**
 * WhatsApp webhook routes for handling Twilio WhatsApp Business API requests
 * Supports dynamic webhooks for different hospital WhatsApp numbers
 */

import express from 'express';
import pkg from 'twilio/lib/twiml/MessagingResponse.js';
const { MessagingResponse } = pkg;
import { logger } from '../lib/logger.js';
import whatsappService from '../services/whatsapp.js';
import { getHospitalByPhone, getHospitalById } from '../lib/hospitals.js';

const router = express.Router();

/**
 * Generic webhook endpoint for incoming WhatsApp messages
 * This endpoint receives messages from Twilio's WhatsApp Business API
 */
router.post('/webhook', async (req, res) => {
  try {
    logger.info('Received WhatsApp webhook request');
    
    // Extract message details from Twilio request
    const from = req.body.From?.replace('whatsapp:', '');
    const to = req.body.To?.replace('whatsapp:', '');
    const body = req.body.Body;
    const mediaUrl = req.body.MediaUrl0;
    const mediaContentType = req.body.MediaContentType0;
    const numMedia = parseInt(req.body.NumMedia || '0', 10);
    
    // Log media details if present
    if (numMedia > 0 && mediaUrl) {
      logger.info(`Received media from ${from}: ${mediaContentType} (${mediaUrl})`);
    }
    
    logger.info(`WhatsApp message from ${from} to ${to}: ${body.substring(0, 100)}${body.length > 100 ? '...' : ''}`);
    
    // Validate required fields
    if (!from || !to || !body) {
      logger.warn('Missing required fields in WhatsApp webhook request');
      return res.status(400).send('Missing required fields');
    }
    
    // Find hospital by phone number
    const hospital = getHospitalByPhone(to);
    if (!hospital) {
      logger.warn(`No hospital found for phone number ${to}`);
      
      // Create TwiML response for unknown hospital
      const twiml = new pkg();
      twiml.message('We apologize, but this WhatsApp number is not registered with our service.');
      
      res.type('text/xml');
      return res.send(twiml.toString());
    }
    
    // Process the message
    const result = await whatsappService.processIncomingMessage({
      from,
      to,
      body,
      mediaUrl,
      mediaContentType,
      numMedia,
      hospitalId: hospital.id
    });
    
    // Create TwiML response
    const twiml = new pkg();
    
    if (result.success) {
      twiml.message(result.message);
    } else {
      // Fallback message in case of error
      twiml.message(result.message || 'We apologize, but we encountered an error processing your message. Please try again later.');
    }
    
    // Send TwiML response
    res.type('text/xml');
    res.send(twiml.toString());
    
    logger.info('WhatsApp webhook request processed successfully');
  } catch (error) {
    logger.error('Error processing WhatsApp webhook:', error);
    
    // Create error TwiML response
    const twiml = new pkg();
    twiml.message('We apologize, but we encountered an error processing your message. Please try again later.');
    
    res.type('text/xml');
    res.send(twiml.toString());
  }
});

/**
 * Hospital-specific webhook endpoint for incoming WhatsApp messages
 * This allows each hospital to have its own dedicated webhook URL
 */
router.post('/webhook/:hospitalId', async (req, res) => {
  try {
    const { hospitalId } = req.params;
    logger.info(`Received WhatsApp webhook request for hospital ${hospitalId}`);
    
    // Validate hospital ID
    const hospital = await getHospitalById(hospitalId);
    if (!hospital) {
      logger.warn(`Invalid hospital ID in webhook URL: ${hospitalId}`);
      return res.status(404).send('Hospital not found');
    }
    
    // Extract message details from Twilio request
    const from = req.body.From?.replace('whatsapp:', '');
    const to = req.body.To?.replace('whatsapp:', '');
    const body = req.body.Body;
    const mediaUrl = req.body.MediaUrl0;
    const mediaContentType = req.body.MediaContentType0;
    const numMedia = parseInt(req.body.NumMedia || '0', 10);
    
    // Log media details if present
    if (numMedia > 0 && mediaUrl) {
      logger.info(`Received media from ${from}: ${mediaContentType} (${mediaUrl})`);
    }
    
    logger.info(`WhatsApp message from ${from} to ${to} for hospital ${hospitalId}: ${body?.substring(0, 100)}${body?.length > 100 ? '...' : ''}`);
    
    // Validate required fields
    if (!from || !to || !body) {
      logger.warn('Missing required fields in WhatsApp webhook request');
      return res.status(400).send('Missing required fields');
    }
    
    // Process the message with the specific hospital ID
    const result = await whatsappService.processIncomingMessage({
      from,
      to,
      body,
      mediaUrl,
      mediaContentType,
      numMedia,
      hospitalId
    });
    
    // Create TwiML response
    const twiml = new pkg();
    
    if (result.success) {
      twiml.message(result.message);
    } else {
      // Fallback message in case of error
      twiml.message(result.message || 'We apologize, but we encountered an error processing your message. Please try again later.');
    }
    
    // Send TwiML response
    res.type('text/xml');
    res.send(twiml.toString());
    
    logger.info(`WhatsApp webhook request for hospital ${hospitalId} processed successfully`);
  } catch (error) {
    logger.error('Error processing hospital-specific WhatsApp webhook:', error);
    
    // Create error TwiML response
    const twiml = new pkg();
    twiml.message('We apologize, but we encountered an error processing your message. Please try again later.');
    
    res.type('text/xml');
    res.send(twiml.toString());
  }
});

/**
 * Health check endpoint for the WhatsApp webhook
 */
router.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', service: 'whatsapp-webhook' });
});

/**
 * Status callback endpoint for WhatsApp messages
 * Twilio calls this endpoint with status updates for sent messages
 */
router.post('/status', (req, res) => {
  try {
    const messageSid = req.body.MessageSid;
    const messageStatus = req.body.MessageStatus;
    
    logger.info(`WhatsApp message ${messageSid} status: ${messageStatus}`);
    
    // Acknowledge receipt of status update
    res.sendStatus(204);
  } catch (error) {
    logger.error('Error processing WhatsApp status callback:', error);
    res.sendStatus(500);
  }
});

export default router;
