"""
Production-level semantic integration for voice agent system.
Handles semantic caching, query processing, and hospital data management.
Optimized for high-concurrency voice calls with sub-100ms response times.
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Tuple
from .cache_manager import redis_manager
from .semantic_processor import semantic_processor
from .fuzzy_matcher import fuzzy_matcher
from .language_config import language_config, get_primary_language, get_supported_languages

# Module-local logger – configuration stays with the host app
logger = logging.getLogger(__name__)

class VoiceAgentSemanticEngine:
    """
    Main semantic engine for the voice agent system.
    Integrates caching, processing, and matching for optimal performance.
    """
    
    def __init__(self):
        self.cache_manager = redis_manager
        self.semantic_processor = semantic_processor
        self.fuzzy_matcher = fuzzy_matcher

        # Async lock for thread-safe metrics updates
        self._metrics_lock = asyncio.Lock()

        # Performance metrics
        self.metrics = {
            "cache_hits": 0,
            "cache_misses": 0,
            "avg_response_time": 0.0,
            "total_queries": 0
        }
    
    async def process_voice_query(self, query: str, hospital_id: str,
                                 language: str = None, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Main entry point for processing voice queries.
        Hindi is the primary language for Indian hospitals.

        Args:
            query: User's voice query (STT output)
            hospital_id: Hospital identifier
            language: Language code (defaults to Hindi)
            context: Call context

        Returns:
            Dict containing response and metadata
        """
        start_time = time.time()

        # Default to Hindi (primary language) if not specified
        if not language:
            language = get_primary_language()

        try:
            # Update metrics (thread-safe)
            async with self._metrics_lock:
                self.metrics["total_queries"] += 1
            
            # Step 1: Quick semantic cache lookup
            cache_result = await self._quick_cache_lookup(query, hospital_id, language)
            if cache_result:
                processing_time = (time.time() - start_time) * 1000

                # Update metrics (thread-safe)
                async with self._metrics_lock:
                    self.metrics["cache_hits"] += 1
                    self._update_avg_response_time_unsafe(processing_time)
                
                return {
                    "response": cache_result["response"],
                    "source": "semantic_cache",
                    "confidence": cache_result["confidence"],
                    "processing_time_ms": processing_time,
                    "cached": True
                }
            
            # Step 2: Process with semantic processor
            async with self._metrics_lock:
                self.metrics["cache_misses"] += 1

            result = await self.semantic_processor.process_query(
                query=query,
                hospital_id=hospital_id,
                language=language,
                context=context,
            )
            
            # Step 3: Cache the result if confidence is high
            if result["confidence"] > 0.8:
                await self._cache_result(query, result["response"], hospital_id, language)
            
            processing_time = (time.time() - start_time) * 1000

            # Update metrics (thread-safe)
            async with self._metrics_lock:
                self._update_avg_response_time_unsafe(processing_time)

            result["processing_time_ms"] = processing_time
            result["cached"] = False

            return result
            
        except Exception as e:
            logger.error(f"Error processing voice query: {e}")
            processing_time = (time.time() - start_time) * 1000
            return {
                "response": language_config.get_error_message(
                    error_type="technical_error",
                    language=language or get_primary_language()
                ),
                "source": "error",
                "confidence": 0.0,
                "processing_time_ms": processing_time,
                "cached": False
            }
    
    async def _quick_cache_lookup(self, query: str, hospital_id: str,
                                 language: str) -> Optional[Dict[str, Any]]:
        """
        Quick cache lookup with language-aware normalization.
        Now uses fully async Redis operations for optimal performance.

        Args:
            query: User query
            hospital_id: Hospital identifier
            language: Language code

        Returns:
            Cached result or None
        """
        try:
            # Normalize query for consistent caching
            normalized_query = self.fuzzy_matcher.multilingual_normalize(query, language)

            # Use async Redis operations directly (no thread pool needed)
            exact_key = f"exact:{hospital_id}:{language}:{normalized_query}"
            exact_result = await self.cache_manager.get_async(exact_key)
            if exact_result:
                return {
                    "response": exact_result,
                    "confidence": 1.0
                }

            # Use async semantic search (no thread pool needed)
            semantic_results = await self.cache_manager.semantic_search_async(
                query, hospital_id, "general", 1
            )

            if semantic_results and semantic_results[0]["similarity"] > 0.9:
                return {
                    "response": semantic_results[0]["response"],
                    "confidence": semantic_results[0]["similarity"]
                }

            return None

        except Exception as e:
            logger.error(f"Error in cache lookup: {e}")
            return None
    
    async def _cache_result(self, query: str, response: str, hospital_id: str,
                           language: str, ttl: int = 86400) -> bool:
        """
        Cache query result with multiple indexing strategies.
        Now uses fully async Redis operations for optimal performance.

        Args:
            query: Original query
            response: System response
            hospital_id: Hospital identifier
            language: Language code
            ttl: Time to live in seconds

        Returns:
            bool: Success or failure
        """
        try:
            # Normalize query
            normalized_query = self.fuzzy_matcher.multilingual_normalize(query, language)

            # Use async Redis operations directly (no thread pool needed)
            exact_key = f"exact:{hospital_id}:{language}:{normalized_query}"
            await self.cache_manager.set_async(exact_key, response, ttl)

            # Cache semantic embedding using async method
            await self.cache_manager.cache_semantic_response_async(
                query, response, hospital_id, "general", ttl
            )

            return True

        except Exception as e:
            logger.error(f"Error caching result: {e}")
            return False
    
    def _update_avg_response_time_unsafe(self, processing_time: float):
        """
        Update average response time metric.
        WARNING: This method is NOT thread-safe. Must be called within self._metrics_lock.
        """
        current_avg = self.metrics["avg_response_time"]
        total_queries = self.metrics["total_queries"]

        # Calculate running average
        self.metrics["avg_response_time"] = (
            (current_avg * (total_queries - 1) + processing_time) / total_queries
        )
    
    async def preload_hospital_cache(self, hospital_id: str,
                                   hospital_data: Dict[str, Any]) -> bool:
        """
        Preload hospital-specific cache with common queries.
        Now uses async operations where possible for better performance.

        Args:
            hospital_id: Hospital identifier
            hospital_data: Hospital data (doctors, tests, etc.)

        Returns:
            bool: Success or failure
        """
        try:
            # Use new dynamic data loading system (eliminates hardcoded values)
            await self.cache_manager.preload_hospital_data_from_config_async(hospital_id, hospital_data)

            # Preload pattern cache for primary language (Hindi)
            from .language_config import get_primary_language
            primary_language = get_primary_language()
            await self.semantic_processor.preload_common_patterns(hospital_id, hospital_data, primary_language)

            # Cache hospital data using async methods
            doctors_data = hospital_data.get("doctors", [])
            tests_data = hospital_data.get("tests", [])

            # Use async Redis operations for hospital data caching
            await self.cache_manager.set_async(
                f"hospital:{hospital_id}:doctors", doctors_data, 3600
            )
            await self.cache_manager.set_async(
                f"hospital:{hospital_id}:tests", tests_data, 3600
            )

            logger.info(f"Preloaded cache for hospital {hospital_id}")
            return True

        except Exception as e:
            logger.error(f"Error preloading hospital cache: {e}")
            return False
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for monitoring.

        Returns:
            Dict containing performance metrics
        """
        # Read metrics in a thread-safe manner
        async with self._metrics_lock:
            total_queries = self.metrics["total_queries"]
            cache_hits = self.metrics["cache_hits"]
            cache_misses = self.metrics["cache_misses"]
            avg_response_time = self.metrics["avg_response_time"]

        cache_hit_rate = 0.0
        if total_queries > 0:
            cache_hit_rate = (cache_hits / total_queries) * 100

        return {
            "total_queries": total_queries,
            "cache_hits": cache_hits,
            "cache_misses": cache_misses,
            "cache_hit_rate_percent": cache_hit_rate,
            "avg_response_time_ms": avg_response_time,
            "redis_stats": self.cache_manager.get_cache_stats()
        }
    
    async def warm_up_cache(self, hospital_configs: List[Dict[str, Any]]) -> bool:
        """
        Warm up cache for multiple hospitals.
        
        Args:
            hospital_configs: List of hospital configurations
            
        Returns:
            bool: Success or failure
        """
        try:
            tasks = []
            for config in hospital_configs:
                hospital_id = config.get("id")
                if hospital_id:
                    task = self.preload_hospital_cache(hospital_id, config)
                    tasks.append(task)
            
            # Execute all preloading tasks concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            success_count = sum(1 for result in results if result is True)
            total_count = len(results)
            
            logger.info(f"Cache warm-up completed: {success_count}/{total_count} hospitals")
            return success_count == total_count
            
        except Exception as e:
            logger.error(f"Error warming up cache: {e}")
            return False
    
    async def clear_hospital_cache(self, hospital_id: str) -> bool:
        """
        Clear cache for a specific hospital.

        Args:
            hospital_id: Hospital identifier

        Returns:
            bool: Success or failure
        """
        try:
            # Clear all cache patterns for this hospital
            patterns = [
                f"exact:{hospital_id}:*",
                f"semantic:{hospital_id}:*",
                f"pattern:{hospital_id}:*",
                f"hospital:{hospital_id}:*"
            ]

            total_cleared = 0
            for pattern in patterns:
                cleared = await self.cache_manager.clear_cache_async(pattern)
                total_cleared += cleared

            logger.info(f"Cleared {total_cleared} cache entries for hospital {hospital_id}")
            return True

        except Exception as e:
            logger.error(f"Error clearing hospital cache: {e}")
            return False

# Global instance
semantic_engine = VoiceAgentSemanticEngine()

# Convenience functions for easy integration
async def process_voice_query(query: str, hospital_id: str, language: str = None,
                            context: Dict[str, Any] = None) -> Dict[str, Any]:
    """Process voice query using the semantic engine. Defaults to Hindi (primary language)."""
    return await semantic_engine.process_voice_query(query, hospital_id, language, context)

async def preload_hospital_cache(hospital_id: str, hospital_data: Dict[str, Any]) -> bool:
    """Preload cache for a hospital."""
    return await semantic_engine.preload_hospital_cache(hospital_id, hospital_data)

async def clear_hospital_cache(hospital_id: str) -> bool:
    """Clear cache for a specific hospital."""
    return await semantic_engine.clear_hospital_cache(hospital_id)

async def get_performance_metrics() -> Dict[str, Any]:
    """Get performance metrics."""
    return await semantic_engine.get_performance_metrics()
