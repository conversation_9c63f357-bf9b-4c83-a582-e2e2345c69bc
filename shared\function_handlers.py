"""
Function handlers for LLM service

This module contains the actual implementation of functions that can be called
by the LLM. These handlers interface with Firebase, PostgreSQL, and other services
to provide real-time data to the LLM.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import asyncio

logger = logging.getLogger(__name__)

# Helper to import test_config with fallback
def _get_test_config_function():
    """Get test config function with fallback."""
    try:
        from .test_config import get_sample_hospital_data
        return get_sample_hospital_data
    except ImportError:
        try:
            from test_config import get_sample_hospital_data
            return get_sample_hospital_data
        except ImportError as e:
            logger.warning(f"Could not import test_config: {e}")
            return None

# Module-level variable to cache the function
_test_config_func = None

def get_sample_hospital_data_with_fallback(hospital_id: str) -> Optional[Dict[str, Any]]:
    """Get sample hospital data with caching and fallback."""
    global _test_config_func
    if _test_config_func is None:
        _test_config_func = _get_test_config_function()
    
    if _test_config_func:
        return _test_config_func(hospital_id)
    return None

class HospitalFunctionHandlers:
    """
    Function handlers for hospital-related operations.
    These functions are called by the LLM to get real-time data and perform actions.
    """
    
    def __init__(self):
        """Initialize function handlers."""
        self.voice_agent_functions = None
        self.whatsapp_agent_functions = None
    
    def set_voice_agent_functions(self, functions):
        """Set voice agent function references."""
        self.voice_agent_functions = functions
    
    def set_whatsapp_agent_functions(self, functions):
        """Set WhatsApp agent function references."""
        self.whatsapp_agent_functions = functions
    
    async def get_hospital_info(self, hospital_id: str) -> Dict[str, Any]:
        """
        Get basic hospital information.
        
        Args:
            hospital_id: Hospital identifier
            
        Returns:
            Dict containing hospital information
        """
        try:
            # Try to get hospital config from voice agent first
            if self.voice_agent_functions and hasattr(self.voice_agent_functions, 'get_hospital_config'):
                config = await self.voice_agent_functions.get_hospital_config(hospital_id)
                return {
                    "success": True,
                    "hospital_id": hospital_id,
                    "name": config.name if config else f"Hospital {hospital_id}",
                    "address": getattr(config, 'address', '') if config else '',
                    "phone": getattr(config, 'phone', '') if config else '',
                    "emergency_number": getattr(config, 'emergency_number', '911') if config else '911'
                }
            
            # Fallback to basic info
            return {
                "success": True,
                "hospital_id": hospital_id,
                "name": f"Hospital {hospital_id}",
                "message": "Basic hospital information available"
            }
            
        except Exception as e:
            logger.error(f"Error getting hospital info: {e}")
            return {
                "success": False,
                "error": str(e),
                "hospital_id": hospital_id
            }
    
    async def get_available_doctors(self, hospital_id: str, date: str = None) -> Dict[str, Any]:
        """
        Get list of available doctors for a specific date.
        
        Args:
            hospital_id: Hospital identifier
            date: Date in YYYY-MM-DD format (optional, defaults to today)
            
        Returns:
            Dict containing list of available doctors
        """
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')
            
            # Try voice agent function first
            if self.voice_agent_functions and hasattr(self.voice_agent_functions, 'get_available_doctors'):
                doctors = await self.voice_agent_functions.get_available_doctors(hospital_id, date)
                return {
                    "success": True,
                    "hospital_id": hospital_id,
                    "date": date,
                    "doctors": doctors,
                    "count": len(doctors)
                }

            # Try WhatsApp agent function
            if self.whatsapp_agent_functions and hasattr(self.whatsapp_agent_functions, 'getDoctors'):
                doctors = await self.whatsapp_agent_functions.getDoctors(hospital_id)
                return {
                    "success": True,
                    "hospital_id": hospital_id,
                    "date": date,
                    "doctors": doctors,
                    "count": len(doctors)
                }

            # Fallback for testing environments
            sample_data = get_sample_hospital_data_with_fallback(hospital_id)
            if sample_data and 'doctors' in sample_data:
                return {
                    "success": True,
                    "hospital_id": hospital_id,
                    "date": date,
                    "doctors": sample_data['doctors'],
                    "count": len(sample_data['doctors']),
                    "source": "fallback_test_data"
                }

            return {
                "success": False,
                "error": "No doctor data source available",
                "hospital_id": hospital_id,
                "date": date
            }
            
        except Exception as e:
            logger.error(f"Error getting available doctors: {e}")
            return {
                "success": False,
                "error": str(e),
                "hospital_id": hospital_id,
                "date": date
            }
    
    async def get_doctor_schedule(self, hospital_id: str, doctor_id: str, date: str = None) -> Dict[str, Any]:
        """
        Get schedule and availability for a specific doctor.
        
        Args:
            hospital_id: Hospital identifier
            doctor_id: Doctor identifier
            date: Date in YYYY-MM-DD format (optional, defaults to today)
            
        Returns:
            Dict containing doctor schedule information
        """
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')
            
            # Try voice agent function
            if self.voice_agent_functions and hasattr(self.voice_agent_functions, 'get_available_time_slots'):
                # Get doctor info first
                doctors = await self.get_available_doctors(hospital_id, date)
                doctor = None
                if doctors.get("success") and doctors.get("doctors"):
                    doctor = next((d for d in doctors["doctors"] if d.get("id") == doctor_id), None)

                if doctor:
                    time_slots = await self.voice_agent_functions.get_available_time_slots(doctor, hospital_id)
                    return {
                        "success": True,
                        "hospital_id": hospital_id,
                        "doctor_id": doctor_id,
                        "doctor_name": doctor.get("name", "Unknown"),
                        "specialty": doctor.get("specialty", "General Medicine"),
                        "date": date,
                        "available_slots": time_slots
                    }

            # Fallback for testing environments
            sample_data = get_sample_hospital_data_with_fallback(hospital_id)
            if sample_data and 'doctors' in sample_data:
                doctor = next((d for d in sample_data['doctors'] if d.get('id') == doctor_id), None)
                if doctor:
                    schedule = doctor.get("schedule", {})
                    from datetime import datetime
                    try:
                        date_obj = datetime.strptime(date, '%Y-%m-%d')
                        day_name = date_obj.strftime('%A')
                        available_slots = schedule.get(day_name, [])
                        return {
                            "success": True,
                            "hospital_id": hospital_id,
                            "doctor_id": doctor_id,
                            "doctor_name": doctor.get("name"),
                            "specialty": doctor.get("specialty"),
                            "date": date,
                            "available_slots": available_slots,
                            "source": "fallback_test_data"
                        }
                    except ValueError:
                        return { "success": False, "error": "Invalid date format for fallback schedule." }

            return {
                "success": False,
                "error": "Doctor not found or schedule unavailable",
                "hospital_id": hospital_id,
                "doctor_id": doctor_id,
                "date": date
            }
            
        except Exception as e:
            logger.error(f"Error getting doctor schedule: {e}")
            return {
                "success": False,
                "error": str(e),
                "hospital_id": hospital_id,
                "doctor_id": doctor_id,
                "date": date
            }
    
    async def get_available_tests(self, hospital_id: str, date: str = None) -> Dict[str, Any]:
        """
        Get list of available medical tests.
        
        Args:
            hospital_id: Hospital identifier
            date: Date in YYYY-MM-DD format (optional, defaults to today)
            
        Returns:
            Dict containing list of available tests
        """
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')
            
            # Try voice agent function first
            if self.voice_agent_functions and hasattr(self.voice_agent_functions, 'get_available_tests'):
                tests = await self.voice_agent_functions.get_available_tests(hospital_id, date)
                return {
                    "success": True,
                    "hospital_id": hospital_id,
                    "date": date,
                    "tests": tests,
                    "count": len(tests)
                }

            # Try WhatsApp agent function
            if self.whatsapp_agent_functions and hasattr(self.whatsapp_agent_functions, 'getTests'):
                tests = await self.whatsapp_agent_functions.getTests(hospital_id)
                return {
                    "success": True,
                    "hospital_id": hospital_id,
                    "date": date,
                    "tests": tests,
                    "count": len(tests)
                }

            # Fallback for testing environments
            sample_data = get_sample_hospital_data_with_fallback(hospital_id)
            if sample_data and 'tests' in sample_data:
                return {
                    "success": True,
                    "hospital_id": hospital_id,
                    "date": date,
                    "tests": sample_data['tests'],
                    "count": len(sample_data['tests']),
                    "source": "fallback_test_data"
                }

            return {
                "success": False,
                "error": "No test data source available",
                "hospital_id": hospital_id,
                "date": date
            }
            
        except Exception as e:
            logger.error(f"Error getting available tests: {e}")
            return {
                "success": False,
                "error": str(e),
                "hospital_id": hospital_id,
                "date": date
            }

    async def book_appointment(self, hospital_id: str, patient_name: str, phone: str,
                             doctor_id: str, date: str, time: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Book an appointment with a doctor.

        Args:
            hospital_id: Hospital identifier
            patient_name: Patient name
            phone: Patient phone number
            doctor_id: Doctor identifier
            date: Appointment date
            time: Appointment time

        Returns:
            Dict containing booking result
        """
        try:
            # Format datetime for the appointment
            appointment_datetime = f"{date} {time}"

            # Try voice agent function first
            if self.voice_agent_functions and hasattr(self.voice_agent_functions, 'save_appointment'):
                appointment_id = await self.voice_agent_functions.save_appointment(
                    hospital_id, patient_name, phone, doctor_id, appointment_datetime
                )

                # Send confirmation SMS if available
                if hasattr(self.voice_agent_functions, 'send_confirmation_sms'):
                    try:
                        # Fetch doctor's name for the SMS
                        doctor_name = "your doctor"
                        doctors_response = await self.get_available_doctors(hospital_id)
                        if doctors_response.get("success"):
                            doctor = next((d for d in doctors_response.get("doctors", []) if d.get("id") == doctor_id), None)
                            if doctor:
                                doctor_name = doctor.get("name", "your doctor")

                        await self.voice_agent_functions.send_confirmation_sms(
                            phone, "appointment",
                            {
                                "patient_name": patient_name,
                                "doctor_name": doctor_name,
                                "time": appointment_datetime
                            },
                            context.get("language", "en"),
                            hospital_id
                        )
                    except Exception as sms_error:
                        logger.warning(f"SMS sending failed: {sms_error}")

                return {
                    "success": True,
                    "appointment_id": appointment_id,
                    "hospital_id": hospital_id,
                    "patient_name": patient_name,
                    "doctor_id": doctor_id,
                    "date": date,
                    "time": time,
                    "message": "Appointment booked successfully"
                }

            return {
                "success": False,
                "error": "Appointment booking service unavailable",
                "hospital_id": hospital_id
            }

        except Exception as e:
            logger.error(f"Error booking appointment: {e}")
            return {
                "success": False,
                "error": str(e),
                "hospital_id": hospital_id,
                "patient_name": patient_name,
                "doctor_id": doctor_id
            }

    async def book_test(self, hospital_id: str, patient_name: str, phone: str,
                       test_id: str, date: str, time: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Book a medical test.

        Args:
            hospital_id: Hospital identifier
            patient_name: Patient name
            phone: Patient phone number
            test_id: Test identifier
            date: Test date
            time: Test time

        Returns:
            Dict containing booking result
        """
        try:
            # Format datetime for the test
            test_datetime = f"{date} {time}"

            # Try voice agent function first
            if self.voice_agent_functions and hasattr(self.voice_agent_functions, 'save_test_booking'):
                await self.voice_agent_functions.save_test_booking(
                    hospital_id, patient_name, phone, test_id, test_datetime
                )

                # Send confirmation SMS if available
                if hasattr(self.voice_agent_functions, 'send_confirmation_sms'):
                    try:
                        await self.voice_agent_functions.send_confirmation_sms(
                            phone, "test",
                            {
                                "patient_name": patient_name,
                                "test_id": test_id,
                                "time": test_datetime
                            },
                            context.get("language", "en"),
                            hospital_id
                        )
                    except Exception as sms_error:
                        logger.warning(f"SMS sending failed: {sms_error}")

                return {
                    "success": True,
                    "hospital_id": hospital_id,
                    "patient_name": patient_name,
                    "test_id": test_id,
                    "date": date,
                    "time": time,
                    "message": "Test booking confirmed successfully"
                }

            return {
                "success": False,
                "error": "Test booking service unavailable",
                "hospital_id": hospital_id
            }

        except Exception as e:
            logger.error(f"Error booking test: {e}")
            return {
                "success": False,
                "error": str(e),
                "hospital_id": hospital_id,
                "patient_name": patient_name,
                "test_id": test_id
            }

# Global instance
hospital_handlers = HospitalFunctionHandlers()

# Function to get all handlers as a dictionary
def get_function_handlers() -> Dict[str, Any]:
    """Get all function handlers as a dictionary for LLM service."""
    return {
        "get_hospital_info": hospital_handlers.get_hospital_info,
        "get_available_doctors": hospital_handlers.get_available_doctors,
        "get_doctor_schedule": hospital_handlers.get_doctor_schedule,
        "get_available_tests": hospital_handlers.get_available_tests,
        "book_appointment": hospital_handlers.book_appointment,
        "book_test": hospital_handlers.book_test
    }
