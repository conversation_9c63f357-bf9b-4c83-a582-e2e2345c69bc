import { withRole } from '../../../../lib/auth';
import { getStaffByHospitalId, createStaffMember } from '../../../../lib/firebase';
import { hashPassword } from '../../../../lib/auth';

// Only admin users can manage staff
export default withRole(async (req, res) => {
  // GET - Fetch staff for a hospital
  if (req.method === 'GET') {
    try {
      const { hospital_id } = req.query;
      
      // If hospital_id is not provided, use the authenticated user's hospital
      const hospitalId = hospital_id || req.user.hospital_id;
      
      // Verify hospital ID matches the authenticated user's hospital
      if (hospitalId !== req.user.hospital_id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to access staff for this hospital'
        });
      }
      
      // Get staff from Firestore
      const result = await getStaffByHospitalId(hospitalId);
      
      if (result.success) {
        // Filter out sensitive information like password_hash
        const sanitizedStaff = result.data.map(staff => {
          const { password_hash, ...sanitizedData } = staff;
          return sanitizedData;
        });
        
        return res.status(200).json({
          success: true,
          data: sanitizedStaff
        });
      } else {
        return res.status(500).json({
          success: false,
          message: result.error || 'Failed to fetch staff'
        });
      }
    } catch (error) {
      console.error('Fetch staff error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // POST - Create new staff member
  if (req.method === 'POST') {
    try {
      const { hospitalId, staffData } = req.body;
      
      // Validate required parameters
      if (!hospitalId || !staffData) {
        return res.status(400).json({
          success: false,
          message: 'Hospital ID and staff data are required'
        });
      }
      
      // Verify hospital ID matches the authenticated user's hospital
      if (hospitalId !== req.user.hospital_id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to create staff for this hospital'
        });
      }
      
      // Validate required staff fields
      if (!staffData.name || !staffData.email || !staffData.role || !staffData.password) {
        return res.status(400).json({
          success: false,
          message: 'Name, email, role, and password are required'
        });
      }
      
      // Hash the password
      const hashedPassword = await hashPassword(staffData.password);
      
      // Prepare staff data for creating in Firestore
      const { password, ...staffDataWithoutPassword } = staffData;
      const staffDataToCreate = {
        ...staffDataWithoutPassword,
        password_hash: hashedPassword,
        hospital_id: hospitalId
      };
      
      // Create staff member in Firestore
      const result = await createStaffMember(staffDataToCreate);
      
      if (result.success) {
        return res.status(201).json({
          success: true,
          message: 'Staff member created successfully',
          id: result.id
        });
      } else {
        return res.status(500).json({
          success: false,
          message: result.error || 'Failed to create staff member'
        });
      }
    } catch (error) {
      console.error('Create staff error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // Method not allowed
  return res.status(405).json({
    success: false,
    message: 'Method not allowed'
  });
}, ['admin']); // Only admin role can access this endpoint