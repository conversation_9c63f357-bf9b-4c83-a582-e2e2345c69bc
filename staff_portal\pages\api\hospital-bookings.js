import { withAuth } from '../../lib/auth';
import { getHospitalDbPool } from '../../lib/pg_utils';
import { logger } from '../../lib/logger';

export default withAuth(async (req, res) => {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }

  const authenticatedHospitalId = req.user.hospital_id; // Changed: Use user's hospital_id

  // Added: Check if hospital_id is present in the token
  if (!authenticatedHospitalId) {
    logger.error('[API_HOSPITAL_BOOKINGS] hospital_id missing from authenticated user token.');
    return res.status(401).json({ success: false, message: 'User not authenticated or hospital ID missing from token.' });
  }

  try {
    const pool = await getHospitalDbPool(authenticatedHospitalId); // Changed: Use authenticatedHospitalId
    
    const query = {
      text: `
        SELECT id, patient_id, doctor_id, hospital_id, start_time, end_time, status, notes 
        FROM appointments 
        WHERE hospital_id = $1 AND DATE(start_time) = CURRENT_DATE
        ORDER BY start_time ASC
      `,
      values: [authenticatedHospitalId], // Changed: Use authenticatedHospitalId
    };

    const { rows } = await pool.query(query);

    return res.status(200).json({ 
      success: true, 
      data: { 
        todayAppointments: rows 
      }
    });

  } catch (error) {
    // Changed: Use authenticatedHospitalId in log
    logger.error(`[API_HOSPITAL_BOOKINGS] Error fetching bookings for hospital ${authenticatedHospitalId}:`, error);
    if (error.message.includes('not found') || error.message.includes('connection string not found') || error.message.includes('Failed to establish connection')) {
        // Changed: Use authenticatedHospitalId in response message
        return res.status(503).json({ success: false, message: `Database service unavailable for hospital ${authenticatedHospitalId}. ${error.message}` });
    }
    return res.status(500).json({ success: false, message: `Internal server error: ${error.message}` });
  }
});
