"""
LLM Integration for Voice Agent

This module integrates the unified LLM service with the voice agent,
replacing the previous fine-tuning approach with function calling.
"""

import logging
import sys
import os
from typing import Dict, Any, Optional

# Add shared directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from shared.llm_service import llm_service
    from shared.function_handlers import hospital_handlers
except ImportError as e:
    logging.error(f"Failed to import shared modules: {e}")
    llm_service = None
    hospital_handlers = None

logger = logging.getLogger(__name__)

class VoiceAgentLLMIntegration:
    """
    Integration class for voice agent LLM functionality.
    Provides a bridge between the voice agent and the unified LLM service.
    """
    
    def __init__(self):
        """Initialize the LLM integration."""
        self.llm_service = llm_service
        self.handlers = hospital_handlers
        self.voice_functions = None
        
        if self.llm_service and self.handlers:
            self._setup_integration()
        else:
            logger.error("LLM service or handlers not available")
    
    def _setup_integration(self):
        """Set up the integration with function handlers."""
        try:
            # Import voice agent functions
            from . import main
            
            # Create a functions object with voice agent functions
            self.voice_functions = type('VoiceFunctions', (), {
                'get_hospital_config': main.get_hospital_config,
                'get_available_doctors': main.get_available_doctors,
                'get_available_tests': main.get_available_tests,
                'get_available_time_slots': main.get_available_time_slots,
                'save_appointment': main.save_appointment,
                'save_test_booking': main.save_test_booking,
                'send_confirmation_sms': main.send_confirmation_sms
            })()
            
            # Set voice agent functions in handlers
            self.handlers.set_voice_agent_functions(self.voice_functions)
            
            # Set function handlers in LLM service
            from shared.function_handlers import get_function_handlers
            self.llm_service.set_function_handlers(get_function_handlers())
            
            logger.info("Voice agent LLM integration setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up LLM integration: {e}")
    
    async def process_voice_input(self, text: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process voice input using the LLM service.
        
        Args:
            text: Voice input text
            context: Context information (hospital_id, language, call_context, etc.)
            
        Returns:
            Dict containing LLM response and any function call results
        """
        if not self.llm_service:
            return {
                "success": False,
                "error": "LLM service not available",
                "response": "I'm sorry, I'm experiencing technical difficulties."
            }
        
        try:
            # Prepare context for LLM
            llm_context = {
                "hospital_id": context.get("hospital_id"),
                "language": context.get("language", "en"),
                "hospital_name": context.get("hospital_name", "the hospital"),
                "user_info": {
                    "phone": context.get("caller_number"),
                    "call_id": context.get("call_id")
                },
                "chat_history": context.get("chat_history", []),
                "state": context.get("state", "greeting")
            }
            
            # Process with LLM service
            result = await self.llm_service.process_message(text, llm_context)
            
            # Add voice-specific processing if needed
            if result.get("success"):
                result["voice_response"] = self._format_for_voice(result.get("response", ""))
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing voice input: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": "I apologize, but I'm experiencing technical difficulties. Please try again."
            }
    
    def _format_for_voice(self, text: str) -> str:
        """
        Format text response for voice output.
        
        Args:
            text: Text to format
            
        Returns:
            Formatted text suitable for voice synthesis
        """
        # Add voice-specific formatting if needed
        # For example, adding pauses, emphasis, etc.
        return text
    
    async def extract_booking_intent(self, text: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract booking intent and details from voice input.
        
        Args:
            text: Voice input text
            context: Context information
            
        Returns:
            Dict containing extracted intent and booking details
        """
        try:
            # Use LLM to extract intent and details
            result = await self.process_voice_input(text, context)
            
            # Extract booking-specific information
            booking_info = {
                "intent": "unknown",
                "booking_type": None,  # "appointment" or "test"
                "patient_name": None,
                "doctor_id": None,
                "test_id": None,
                "date": None,
                "time": None,
                "confidence": 0.0
            }
            
            # Analyze function calls for booking intent
            if result.get("function_calls"):
                for call in result["function_calls"]:
                    if call["name"] in ["book_appointment", "book_test"]:
                        booking_info["intent"] = "booking"
                        booking_info["booking_type"] = "appointment" if call["name"] == "book_appointment" else "test"
                        booking_info.update(call.get("arguments", {}))
                        booking_info["confidence"] = 0.9
                        break
                    elif call["name"] in ["get_available_doctors", "get_doctor_schedule"]:
                        booking_info["intent"] = "doctor_inquiry"
                        booking_info["confidence"] = 0.8
                    elif call["name"] == "get_available_tests":
                        booking_info["intent"] = "test_inquiry"
                        booking_info["confidence"] = 0.8
            
            # Analyze response text for intent keywords
            response_lower = result.get("response", "").lower()
            if any(word in response_lower for word in ["appointment", "book", "schedule"]):
                if booking_info["intent"] == "unknown":
                    booking_info["intent"] = "booking_inquiry"
                    booking_info["confidence"] = 0.6
            
            return {
                "success": True,
                "booking_info": booking_info,
                "llm_result": result
            }
            
        except Exception as e:
            logger.error(f"Error extracting booking intent: {e}")
            return {
                "success": False,
                "error": str(e),
                "booking_info": {"intent": "unknown", "confidence": 0.0}
            }
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get LLM usage statistics."""
        if self.llm_service:
            return self.llm_service.get_usage_stats()
        return {"error": "LLM service not available"}
    
    def reset_usage_stats(self):
        """Reset LLM usage statistics."""
        if self.llm_service:
            self.llm_service.reset_usage_stats()

# Global instance
voice_llm_integration = VoiceAgentLLMIntegration()

# Convenience functions for voice agent
async def process_voice_input(text: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Process voice input using the integrated LLM service."""
    return await voice_llm_integration.process_voice_input(text, context)

async def extract_booking_intent(text: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Extract booking intent from voice input."""
    return await voice_llm_integration.extract_booking_intent(text, context)

def get_llm_usage_stats() -> Dict[str, Any]:
    """Get LLM usage statistics."""
    return voice_llm_integration.get_usage_stats()

def reset_llm_usage_stats():
    """Reset LLM usage statistics."""
    voice_llm_integration.reset_usage_stats()
