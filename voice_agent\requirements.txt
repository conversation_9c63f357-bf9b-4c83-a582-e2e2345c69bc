# Voice Agent Requirements
# Note: LLM functionality (OpenAI) has been moved to shared/llm_service.py
# Voice agent now communicates with shared LLM service via HTTP API

# Core web framework - Updated for Python 3.13 compatibility
fastapi==0.115.13
uvicorn==0.34.3
pydantic==2.11.7

# Firebase and database
firebase-admin==6.2.0
psycopg2-binary==2.9.10

# Voice processing
google-cloud-texttospeech==2.26.0

# Network and SSH
sshtunnel==0.4.0
paramiko==3.5.1
requests==2.32.4  # For HTTP calls to shared LLM service

# LLM and AI
openai==1.88.0  # For OpenAI API integration
python-dotenv==1.1.0  # For environment variable management

# jambonz-node-client-sdk==0.1.0

# Semantic Processing Dependencies for Voice Agent
# Production-grade libraries for high-performance semantic caching

# Core semantic processing - Multilingual support for Indian languages
sentence-transformers==4.1.0   # Upgrade to fix PyTorch model deserialization vulnerability
transformers==4.52.4
torch==2.7.1

# Fast fuzzy matching
rapidfuzz==3.13.0

# Enhanced Redis support - Updated for Python 3.13 compatibility with asyncio support
redis==5.2.1

# Scientific computing - Updated for Python 3.13 compatibility
numpy==2.2.5
scipy==1.15.3

# Text processing - Updated for Python 3.13 compatibility
nltk==3.9.1
spacy==3.8.7

# Performance monitoring
psutil==7.0.0

# Optional: GPU acceleration (uncomment if using GPU)
# torch-audio==2.1.1
# torchaudio==2.1.1

# Development and testing - Updated for Python 3.13 compatibility
pytest==8.4.0
pytest-asyncio==1.0.0
