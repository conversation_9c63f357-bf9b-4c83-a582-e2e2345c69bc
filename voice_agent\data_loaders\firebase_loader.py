"""
Firebase data loader for voice agent system.
Loads hospital data (doctors, tests) from Firebase Firestore.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from ..database import get_firestore_db

logger = logging.getLogger(__name__)


class FirebaseDataLoader:
    """
    Production-ready Firebase data loader for hospital information.
    Handles doctors, tests, and hospital configuration data.
    """

    def __init__(self):
        """Initialize Firebase data loader."""
        self.db = None
        self._initialize_db()

    def _initialize_db(self):
        """Initialize Firestore database connection."""
        try:
            self.db = get_firestore_db()
            logger.info("Firebase data loader initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Firebase data loader: {e}")
            self.db = None

    async def load_hospital_data(self, hospital_id: str) -> Dict[str, Any]:
        """
        Load complete hospital data from Firebase.

        Args:
            hospital_id: Hospital identifier

        Returns:
            Dict containing doctors, tests, and hospital info
        """
        if not self.db:
            logger.error("Firebase database not initialized")
            return {}

        try:
            hospital_data = {
                "hospital_id": hospital_id,
                "doctors": await self._load_doctors(hospital_id),
                "tests": await self._load_tests(hospital_id),
                "hospital_info": await self._load_hospital_info(hospital_id),
                "languages": ["hi", "bn", "en"]  # Default supported languages
            }

            logger.info(f"Loaded hospital data for {hospital_id}: "
                       f"{len(hospital_data['doctors'])} doctors, "
                       f"{len(hospital_data['tests'])} tests")

            return hospital_data

        except Exception as e:
            logger.error(f"Error loading hospital data for {hospital_id}: {e}")
            return {}

    async def _load_doctors(self, hospital_id: str) -> List[Dict[str, Any]]:
        """Load doctors data from Firebase."""
        try:
            # Use the existing pattern from database.py
            collection_path = f'hospital_{hospital_id}_data'
            doctors_ref = self.db.collection(collection_path).document('doctors').collection('doctors')
            
            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            docs = await loop.run_in_executor(None, doctors_ref.get)
            doctors = []
            
            for doc in docs:
                doctor_data = doc.to_dict()
                doctor_data['id'] = doc.id
                
                # Ensure required fields exist
                doctor_data.setdefault('name', 'Unknown Doctor')
                doctor_data.setdefault('specialty', 'General Medicine')
                doctor_data.setdefault('schedule', 'Contact hospital for schedule')
                
                doctors.append(doctor_data)
            
            return doctors

        except Exception as e:
            logger.error(f"Error loading doctors for hospital {hospital_id}: {e}")
            return []

    async def _load_tests(self, hospital_id: str) -> List[Dict[str, Any]]:
        """Load tests data from Firebase."""
        try:
            # Use the existing pattern from database.py
            collection_path = f'hospital_{hospital_id}_data'
            tests_ref = self.db.collection(collection_path).document('test_info').collection('tests')
            
            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            docs = await loop.run_in_executor(None, tests_ref.get)
            tests = []
            
            for doc in docs:
                test_data = doc.to_dict()
                test_data['id'] = doc.id
                
                # Ensure required fields exist
                test_data.setdefault('name', 'Unknown Test')
                test_data.setdefault('price', 'Contact hospital for pricing')
                test_data.setdefault('duration', 'Contact hospital for duration')
                
                tests.append(test_data)
            
            return tests

        except Exception as e:
            logger.error(f"Error loading tests for hospital {hospital_id}: {e}")
            return []

    async def _load_hospital_info(self, hospital_id: str) -> Dict[str, Any]:
        """Load hospital information from Firebase."""
        try:
            # Try to load hospital info from the hospitals collection
            hospital_ref = self.db.collection('hospitals').document(hospital_id)
            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            hospital_doc = await loop.run_in_executor(None, hospital_ref.get)
            
            if hospital_doc.exists:
                hospital_info = hospital_doc.to_dict()
                hospital_info['id'] = hospital_doc.id
                return hospital_info
            else:
                logger.warning(f"Hospital info not found for {hospital_id}")
                return {
                    "id": hospital_id,
                    "name": f"Hospital {hospital_id}",
                    "languages": ["hi", "bn", "en"]
                }

        except Exception as e:
            logger.error(f"Error loading hospital info for {hospital_id}: {e}")
            return {
                "id": hospital_id,
                "name": f"Hospital {hospital_id}",
                "languages": ["hi", "bn", "en"]
            }

    def load_hospital_data_sync(self, hospital_id: str) -> Dict[str, Any]:
        """
        Synchronous version of load_hospital_data.
        
        Args:
            hospital_id: Hospital identifier
            
        Returns:
            Dict containing hospital data
        """
        try:
            # Re-use running loop if already inside async context
            loop = asyncio.get_running_loop()
            # Use run_coroutine_threadsafe for running async code from sync context within existing loop
            future = asyncio.run_coroutine_threadsafe(self.load_hospital_data(hospital_id), loop)
            return future.result()
        except RuntimeError:
            # Fallback for pure sync contexts (no running loop)
            return asyncio.run(self.load_hospital_data(hospital_id))
        except Exception as e:
            logger.error(f"Error in sync hospital data loading: {e}")
            return {}

    async def get_all_hospitals(self) -> List[str]:
        """
        Get list of all hospital IDs from Firebase.
        
        Returns:
            List of hospital IDs
        """
        if not self.db:
            logger.error("Firebase database not initialized")
            return []

        try:
            hospitals_ref = self.db.collection('hospitals')
            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            docs = await loop.run_in_executor(None, hospitals_ref.get)
            
            hospital_ids = []
            for doc in docs:
                hospital_ids.append(doc.id)
            
            logger.info(f"Found {len(hospital_ids)} hospitals in Firebase")
            return hospital_ids

        except Exception as e:
            logger.error(f"Error getting hospital list: {e}")
            return []
