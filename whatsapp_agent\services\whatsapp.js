/**
 * WhatsApp service for handling message processing and bookings
 */

import { v4 as uuidv4 } from 'uuid';
import { logger } from '../lib/logger.js';
import * as ai from '../lib/ai.js';
import * as db from '../lib/database.js';
import * as firebase from '../lib/firebase.js';
import * as redis from '../lib/redis.js';
import * as hospitals from '../lib/hospitals.js';

/**
 * Process an incoming WhatsApp message
 * @param {Object} message - WhatsApp message data
 * @returns {Promise<Object>} - Response message
 */
export const processIncomingMessage = async (message) => {
  try {
    const { from, body, mediaUrl, mediaContentType, numMedia, hospitalId } = message;
    
    // Validate hospital has WhatsApp service enabled
    if (!hospitals.hasWhatsAppService(hospitalId)) {
      logger.warn(`Hospital ${hospitalId} does not have WhatsApp service enabled`);
      return {
        success: false,
        message: 'This hospital does not have WhatsApp service enabled. Please contact the hospital directly.',
        error: 'WhatsApp service not enabled for this hospital'
      };
    }
    
    // Generate a unique message ID
    const messageId = uuidv4();
    
    // Save incoming message to database
    await db.saveWhatsAppMessage({
      hospitalId,
      messageId,
      phoneNumber: from,
      direction: 'inbound',
      messageText: body,
      mediaUrl,
      mediaContentType,
      timestamp: new Date(),
      metadata: {
        numMedia: numMedia || 0,
        mediaContentType
      }
    });
    
    // Get chat history for context
    const chatHistory = await db.getWhatsAppChatHistory(hospitalId, from, 10);
    
    // Get hospital information
    const hospital = hospitals.getHospitalById(hospitalId);
    
    // Get available doctors and tests from Firebase
    const doctors = await firebase.getDoctors(hospitalId);
    const tests = await firebase.getTests(hospitalId);
    
    // Check if this is an image message that might contain a prescription
    let ocrResult = null;
    let prescriptionDetails = null;
    let isImageMessage = false;
    
    // Import required modules dynamically to avoid circular dependencies
    const ocr = await import('../lib/ocr.js').then(m => m.default);
    const staffNotification = await import('../lib/staffNotification.js').then(m => m.default);
    
    if (numMedia > 0 && mediaUrl && mediaContentType && mediaContentType.startsWith('image/')) {
      isImageMessage = true;
      
      logger.info(`Processing image from ${from} with OCR`);
      
      // Process image with OCR
      ocrResult = await ocr.processImage(mediaUrl);
      
      if (ocrResult.success) {
        logger.info(`OCR successful for image from ${from}. Confidence: ${ocrResult.confidence}`);
        
        // Extract prescription details
        prescriptionDetails = await ocr.extractPrescriptionDetails(ocrResult.text);
        
        // If confidence is low, notify staff portal
        if (ocrResult.confidence < 0.7) { // Threshold can be adjusted
          await staffNotification.sendLowConfidenceNotification({
            hospitalId,
            phoneNumber: from,
            mediaUrl,
            ocrText: ocrResult.text,
            confidence: ocrResult.confidence,
            prescriptionDetails,
            timestamp: new Date()
          });
          
          logger.warn(`Low OCR confidence (${ocrResult.confidence}), staff notified`);
        }
      } else {
        // OCR failed, notify staff
        await staffNotification.sendOcrErrorNotification({
          hospitalId,
          phoneNumber: from,
          mediaUrl,
          error: ocrResult.error,
          timestamp: new Date()
        });
        
        logger.error(`OCR failed for image from ${from}: ${ocrResult.error}`);
      }
    }
    
    // Detect language (default to first language in hospital settings)
    const defaultLanguage = hospital.languages && hospital.languages.length > 0 
      ? hospital.languages[0] 
      : 'en';
    
    // Prepare custom context for AI
    let customContext = {};
    
    // If we have OCR results, add to AI context
    if (ocrResult && ocrResult.success) {
      customContext.ocrText = ocrResult.text;
      customContext.prescriptionDetails = prescriptionDetails;
      
      // If we extracted test details, prepare context
      if (prescriptionDetails && prescriptionDetails.tests) {
        const matchingTests = tests.filter(test => {
          // Simple fuzzy search for test names
          return test.name.toLowerCase().includes(prescriptionDetails.tests.toLowerCase()) ||
                 prescriptionDetails.tests.toLowerCase().includes(test.name.toLowerCase());
        });
        
        if (matchingTests.length > 0) {
          customContext.matchingTests = matchingTests;
        }
      }
    }
    
    // Special handling for image-only messages (no text)
    const messageText = body || (isImageMessage ? 'I sent you a prescription image.' : '');
    
    // Process message with AI
    const processedMessage = await ai.processMessage(
      {
        text: messageText,
        phoneNumber: from,
        language: defaultLanguage,
        chatHistory,
        ocrContext: customContext
      },
      {
        hospitalName: hospital.name,
        doctors,
        tests
      }
    );
    
    // Handle different intents
    let responseText = processedMessage.response;
    
    // If we have OCR results, add them to the response for clarity
    if (ocrResult && ocrResult.success && prescriptionDetails) {
      // For prescription images with extracted test details
      if (prescriptionDetails.tests) {
        const confidenceInfo = ocrResult.confidence < 0.8 ? 
          ' (Note: The image was a bit unclear, so our interpretation might not be 100% accurate)' : '';
          
        // Add extracted information at the beginning of the response
        responseText = `I've analyzed your prescription${confidenceInfo}. ` + responseText;
      } else if (isImageMessage) {
        // For images that we couldn't identify as prescriptions
        if (ocrResult.confidence < 0.6) {
          await staffNotification.requestManualReview({
            hospitalId,
            phoneNumber: from,
            mediaUrl,
            ocrText: ocrResult.text,
            reason: 'Unable to extract test details from prescription',
            timestamp: new Date()
          });
          
          responseText = `I've received your image, but I'm having trouble understanding what's in the prescription. One of our staff members will review it and get back to you soon. ` + responseText;
        }
      }
    }
    
    if (processedMessage.intent === 'booking_request' && processedMessage.bookingDetails) {
      // Handle booking request
      const bookingResult = await handleBookingRequest(
        hospitalId,
        from,
        processedMessage.bookingDetails,
        doctors,
        tests
      );
      
      if (bookingResult.success) {
        responseText = bookingResult.message;
      }
    } else if (processedMessage.intent === 'availability_query' && processedMessage.bookingDetails?.doctor_id) {
      // Handle doctor availability query
      const availabilityResult = await handleAvailabilityQuery(
        hospitalId,
        processedMessage.bookingDetails.doctor_id,
        doctors
      );
      
      if (availabilityResult.success) {
        responseText = availabilityResult.message;
      }
    }
    
    // Save outgoing message to database
    const outgoingMessageId = uuidv4();
    await db.saveWhatsAppMessage({
      hospitalId,
      messageId: outgoingMessageId,
      phoneNumber: from,
      direction: 'outbound',
      messageText: responseText,
      timestamp: new Date(),
      metadata: {
        intent: processedMessage.intent,
        language: processedMessage.language,
        ocrApplied: isImageMessage,
        ocrConfidence: ocrResult ? ocrResult.confidence : null,
        prescriptionData: prescriptionDetails || null
      }
    });
    
    return {
      success: true,
      message: responseText,
      language: processedMessage.language
    };
  } catch (error) {
    logger.error('Error processing WhatsApp message:', error);
    return {
      success: false,
      error: 'Error processing message',
      message: 'We apologize, but we encountered an error processing your message. Please try again later.'
    };
  }
};

/**
 * Handle a booking request
 * @param {string} hospitalId - Hospital ID
 * @param {string} phoneNumber - Customer phone number
 * @param {Object} bookingDetails - Extracted booking details
 * @param {Array} doctors - Available doctors
 * @param {Array} tests - Available tests
 * @returns {Promise<Object>} - Booking result
 */
async function handleBookingRequest(hospitalId, phoneNumber, bookingDetails, doctors, tests) {
  try {
    const { patient_name, doctor_id, test_id, date, time } = bookingDetails;
    
    // Validate required fields
    if (!patient_name || (!doctor_id && !test_id) || !date || !time) {
      return {
        success: false,
        message: 'I need more information to complete your booking. Please provide your name, the doctor or test, and your preferred date and time.'
      };
    }
    
    // Create or update patient record
    const patientId = uuidv4();
    await db.createOrUpdatePatient({
      id: patientId,
      name: patient_name,
      phone: phoneNumber,
      hospitalId
    });
    
    // Handle doctor appointment
    if (doctor_id) {
      return await handleDoctorBooking(
        hospitalId,
        patientId,
        patient_name,
        doctor_id,
        date,
        time,
        doctors
      );
    }
    
    // Handle test booking
    if (test_id) {
      return await handleTestBooking(
        hospitalId,
        patientId,
        patient_name,
        test_id,
        date,
        time,
        tests
      );
    }
    
    return {
      success: false,
      message: 'I couldn\'t process your booking request. Please specify either a doctor or a test.'
    };
  } catch (error) {
    logger.error('Error handling booking request:', error);
    return {
      success: false,
      message: 'We encountered an error while processing your booking. Please try again later.'
    };
  }
};

/**
 * Handle a doctor appointment booking
 * @param {string} hospitalId - Hospital ID
 * @param {string} patientId - Patient ID
 * @param {string} patientName - Patient name
 * @param {string} doctorId - Doctor ID
 * @param {string} date - Appointment date
 * @param {string} time - Appointment time
 * @param {Array} doctors - Available doctors
 * @returns {Promise<Object>} - Booking result
 */
async function handleDoctorBooking(hospitalId, patientId, patientName, doctorId, date, time, doctors) {
  try {
    // Find doctor in the available doctors list
    const doctor = doctors.find(d => d.id === doctorId);
    if (!doctor) {
      return {
        success: false,
        message: `I couldn't find the doctor you requested. Please choose from our available doctors.`
      };
    }
    
    // Check booking limit
    const bookingStatus = await firebase.checkDoctorBookingLimit(hospitalId, doctorId, date);
    if (!bookingStatus.isAvailable) {
      return {
        success: false,
        message: `I'm sorry, but Dr. ${doctor.name} is fully booked on ${date}. Would you like to choose another doctor or date?`
      };
    }
    
    // Parse date and time
    const appointmentDateTime = new Date(`${date}T${time}`);
    
    // Calculate end time (default to 30 minutes)
    const hospital = hospitals.getHospitalById(hospitalId);
    const durationMinutes = hospital.settings?.appointment_duration_minutes || 30;
    const endTime = new Date(appointmentDateTime.getTime() + durationMinutes * 60000);
    
    // Create appointment with 6-digit ID (generated in database.js)
    const result = await db.createAppointment({
      patientId,
      doctorId,
      hospitalId,
      startTime: appointmentDateTime,
      endTime,
      status: 'scheduled',
      notes: 'Booked via WhatsApp',
      phoneNumber: patientId // Using patient ID as phone number for ID generation
    });
    
    const appointmentId = result.id;
    
    return {
      success: true,
      message: `Great! I've booked your appointment with Dr. ${doctor.name} on ${date} at ${time}. Please arrive 15 minutes before your appointment. Your booking reference is ${appointmentId}.`
    };
  } catch (error) {
    logger.error('Error handling doctor booking:', error);
    return {
      success: false,
      message: 'We encountered an error while processing your doctor appointment. Please try again later.'
    };
  }
}

/**
 * Handle a test booking
 * @param {string} hospitalId - Hospital ID
 * @param {string} patientId - Patient ID
 * @param {string} patientName - Patient name
 * @param {string} testId - Test ID
 * @param {string} date - Test date
 * @param {string} time - Test time
 * @param {Array} tests - Available tests
 * @returns {Promise<Object>} - Booking result
 */
async function handleTestBooking(hospitalId, patientId, patientName, testId, date, time, tests) {
  try {
    // Find test in the available tests list
    const test = tests.find(t => t.id === testId);
    if (!test) {
      return {
        success: false,
        message: `I couldn't find the test you requested. Please choose from our available tests.`
      };
    }
    
    // Parse date and time
    const testDateTime = new Date(`${date}T${time}`);
    
    // Use database.js to create test booking with 6-digit ID
    const bookingResult = await db.createTestBooking({
      patientName,
      patientId, // Using patient ID as phone for now
      testId,
      hospitalId,
      bookingTime: testDateTime,
      status: 'scheduled',
      notes: 'Booked via WhatsApp',
      phoneNumber: patientId // For ID generation
    });
    const bookingId = bookingResult.id;
    
    return {
      success: true,
      message: `Great! I've booked your ${test.name} test on ${date} at ${time}. Please arrive 15 minutes before your appointment and bring any required documentation. Your booking reference is ${bookingId}.`
    };
  } catch (error) {
    logger.error('Error handling test booking:', error);
    return {
      success: false,
      message: 'We encountered an error while processing your test booking. Please try again later.'
    };
  }
}

/**
 * Handle a doctor availability query
 * @param {string} hospitalId - Hospital ID
 * @param {string} doctorId - Doctor ID
 * @param {Array} doctors - Available doctors
 * @returns {Promise<Object>} - Availability result
 */
async function handleAvailabilityQuery(hospitalId, doctorId, doctors) {
  try {
    // Find doctor in the available doctors list
    const doctor = doctors.find(d => d.id === doctorId);
    if (!doctor) {
      return {
        success: false,
        message: `I couldn't find information about the doctor you're asking for. Please specify one of our available doctors.`
      };
    }
    
    // Get real-time availability from Redis
    const availabilityMessage = await redis.formatDoctorAvailabilityMessage(
      hospitalId,
      doctorId,
      doctor.name
    );
    
    return {
      success: true,
      message: availabilityMessage
    };
  } catch (error) {
    logger.error('Error handling availability query:', error);
    return {
      success: false,
      message: `We're unable to retrieve the doctor's availability information at the moment. Please try again later.`
    };
  }
}

export default {
  processIncomingMessage
};
