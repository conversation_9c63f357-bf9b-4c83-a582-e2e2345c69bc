import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { MagnifyingGlassIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';
import Layout from '../components/Layout'; // Assuming you have a Layout component

// Helper function to render result items as tiles without buttons
const renderResultItem = (item, type) => {
  // Customize this based on the actual data structure for each type
  let title = item.name || item.patient_name || 'N/A';
  let details = '';
  let link = '#';
  let additionalInfo = '';

  switch (type) {
    case 'doctors':
      title = item.name || 'Unnamed Doctor';
      details = `Specialization: ${item.specialization || 'N/A'}`;
      additionalInfo = item.email ? `Email: ${item.email}` : '';
      link = item.id ? `/appointments?doctorId=${item.id}` : '#';
      break;
    case 'staff':
      title = item.name || 'Unnamed Staff';
      details = `Role: ${item.role || 'N/A'}`;
      additionalInfo = item.email ? `Email: ${item.email}` : '';
      link = item.id ? `/staff/${item.id}` : '#';
      break;
    case 'patients':
      title = item.name || 'Unnamed Patient';
      details = `Phone: ${item.phone || 'N/A'}`;
      additionalInfo = item.email ? `Email: ${item.email}` : '';
      link = item.id ? `/patients/${item.id}` : '#';
      break;
    case 'appointments':
      title = `Appointment for ${item.patient_name || 'N/A'} with Dr. ${item.doctor_name || 'N/A'}`;
      details = `Time: ${new Date(item.start_time).toLocaleString()}`;
      additionalInfo = `Status: ${item.status}`;
      link = item.id ? `/appointments?id=${item.id}` : '#';
      break;
    case 'testBookings':
      title = `Test Booking for ${item.patient_name || 'N/A'}`;
      details = `Test: ${item.test_type_name || item.test_type_id}`;
      additionalInfo = `Time: ${new Date(item.booking_time).toLocaleString()} - Status: ${item.status}`;
      link = item.id ? `/test-bookings/${item.id}` : '#';
      break;
    default:
      details = JSON.stringify(item);
  }

  return (
    <li key={item.id || JSON.stringify(item)} className="bg-white shadow-sm border border-slate-200 overflow-hidden rounded-lg px-6 py-4 mb-3 hover:bg-slate-50 transition-colors duration-150">
      <a href={link} className="block">
        <h4 className="text-lg font-medium text-[#003253] truncate">{title}</h4>
        <p className="text-sm text-slate-600 mt-1">{details}</p>
        {additionalInfo && <p className="text-sm text-slate-500 mt-1">{additionalInfo}</p>}
      </a>
    </li>
  );
};

const SearchPage = () => {
  const router = useRouter();
  const { q } = router.query; // Get search term from URL query parameter 'q'
  const [searchTerm, setSearchTerm] = useState('');
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (q) {
      setSearchTerm(q);
      performSearch(q);
    }
  }, [q]);
  
  // Get search term from URL on initial load
  useEffect(() => {
    const { q } = router.query;
    if (q && q !== searchTerm) {
      setSearchTerm(q);
      performSearch(q);
    }
  }, []);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const performSearch = async (termToSearch) => {
    if (!termToSearch || termToSearch.trim().length < 2) {
      setError('Search term must be at least 2 characters.');
      setResults(null);
      return;
    }
    setLoading(true);
    setError('');
    setResults(null);

    try {
      const response = await fetch(`/api/search?term=${encodeURIComponent(termToSearch)}`);
      const data = await response.json();
      if (response.ok && data.success) {
        setResults(data.data);
      } else {
        setError(data.message || 'Search failed. Please try again.');
        setResults(null);
      }
    } catch (err) {
      console.error('Search API error:', err);
      setError('An unexpected error occurred. Please try again later.');
      setResults(null);
    }
    setLoading(false);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    // Update URL to reflect the search term, triggering useEffect or direct search
    router.push(`/search?q=${encodeURIComponent(searchTerm)}`, undefined, { shallow: true });
    if (searchTerm !== q) { // If term changed manually and submitted, perform search
        performSearch(searchTerm);
    }
  };
  
  const resultCategories = results ? Object.keys(results).filter(key => results[key] && results[key].length > 0) : [];

  return (
    <Layout title="Search">
      <div>
        <div className="mb-8">
          <h1 className="text-3xl font-bold leading-tight text-slate-900">Search</h1>
        </div>

        <form onSubmit={handleSearchSubmit} className="mb-8 flex items-center">
          <input
            type="search"
            name="search"
            id="search"
            value={searchTerm}
            onChange={handleSearchChange}
            placeholder="Search by name, ID, phone, etc."
            className="shadow-sm focus:ring-[#003253] focus:border-[#003253] block w-full sm:text-sm border-slate-300 rounded-md py-3 px-4"
            required
            minLength={2}
          />
          <button
            type="submit"
            disabled={loading}
            className="ml-3 inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-[#003253] hover:bg-[#004b7a] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#003253] disabled:opacity-50"
          >
            <MagnifyingGlassIcon className="h-5 w-5 mr-2" />
            {loading ? 'Searching...' : 'Search'}
          </button>
        </form>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-center">
            <ExclamationCircleIcon className="h-5 w-5 mr-3 text-red-400" />
            <p>{error}</p>
          </div>
        )}

        {loading && (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#003253] mr-3"></div>
            <p className="text-slate-500">Loading search results...</p>
          </div>
        )}

        {!loading && results && resultCategories.length === 0 && searchTerm.length >= 2 && (
          <p className="text-center text-slate-500 py-8">No results found for "{searchTerm}".</p>
        )}
        
        {!loading && results && resultCategories.length > 0 && (
          <div className="space-y-8">
            {resultCategories.map(categoryKey => (
              <section key={categoryKey}>
                <h2 className="text-xl font-semibold text-slate-800 mb-3 capitalize">
                  {categoryKey.replace(/([A-Z])/g, ' $1').trim()} ({results[categoryKey].length})
                </h2>
                {results[categoryKey].length > 0 ? (
                  <ul className="space-y-3">
                    {results[categoryKey].map(item => renderResultItem(item, categoryKey))}
                  </ul>
                ) : (
                  <p className="text-sm text-slate-500">No {categoryKey.toLowerCase()} found.</p>
                )}
              </section>
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default SearchPage;
