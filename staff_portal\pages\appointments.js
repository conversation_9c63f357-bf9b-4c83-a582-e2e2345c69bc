import React, { useState, useEffect, useCallback } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Layout from '../components/Layout';
import { ChevronLeft, ChevronRight, ArrowUp, ArrowDown, Filter, Calendar, User, Search } from 'react-feather';

const AppointmentsPage = () => {
  const router = useRouter();
  const [appointments, setAppointments] = useState([]);
  const [doctorsMap, setDoctorsMap] = useState({}); // Stores { doctorId: { name: 'Dr. Foo' } }
  const [doctors, setDoctors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [patientSearchTerm, setPatientSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    startDate: '',
    endDate: '',
    status: '',
    doctorId: '',
    patientId: '',
    sortBy: 'start_time',
    sortOrder: 'DESC',
  });
  const [pagination, setPagination] = useState({
    totalItems: 0,
    totalPages: 1,
    currentPage: 1,
    pageSize: 10,
  });

  const fetchAppointments = useCallback(async (currentFilters) => {
    setLoading(true);
    setError('');
    try {
      const queryParams = new URLSearchParams();
      Object.entries(currentFilters).forEach(([key, value]) => {
        if (value !== null && value !== '') {
          queryParams.append(key, value);
        }
      });

      const response = await fetch(`/api/appointments?${queryParams.toString()}`);
      const data = await response.json();

      if (response.ok && data.success) {
        setAppointments(data.data.appointments || []);
        setPagination(data.data.pagination || { totalItems: 0, totalPages: 1, currentPage: 1, pageSize: currentFilters.limit });
      } else {
        setError(data.message || 'Failed to fetch appointments.');
        setAppointments([]);
      }
    } catch (err) {
      console.error('Fetch appointments error:', err);
      setError('An unexpected error occurred while fetching appointments.');
      setAppointments([]);
    }
    setLoading(false);
  }, []);

  // Fetch all doctors for the hospital
  const fetchDoctors = useCallback(async () => {
    try {
      const response = await fetch('/api/doctors');
      const data = await response.json();
      
      if (response.ok && data.success) {
        setDoctors(data.data || []);
      } else {
        console.error('Failed to fetch doctors:', data.message);
      }
    } catch (err) {
      console.error('Error fetching doctors:', err);
    }
  }, []);

  useEffect(() => {
    // Initialize filters from URL query params if present, then fetch
    const initialFilters = { ...filters };
    let hasQueryParams = false;
    Object.keys(filters).forEach(key => {
      if (router.query[key]) {
        initialFilters[key] = router.query[key];
        if (key === 'page' || key === 'limit') initialFilters[key] = parseInt(router.query[key], 10);
        hasQueryParams = true;
      }
    });
    if(hasQueryParams) {
        setFilters(initialFilters);
    }
    fetchAppointments(initialFilters);
    fetchDoctors(); // Fetch all doctors for the dropdown
  }, [router.query, fetchAppointments, fetchDoctors]); // Re-fetch if query params change

  // Fetch doctor details when appointments change
  useEffect(() => {
    const fetchDoctorDetails = async (doctorId) => {
      if (!doctorId || doctorsMap[doctorId]) return; // Don't fetch if no ID or already fetched
      try {
        // console.log(`Fetching doctor details for ${doctorId}`);
        const response = await fetch(`/api/doctors/${doctorId}`);
        const result = await response.json();
        if (result.success && result.data) {
          setDoctorsMap(prevMap => ({
            ...prevMap,
            [doctorId]: result.data,
          }));
        }
      } catch (err) {
        console.error(`Failed to fetch doctor ${doctorId}:`, err);
        // Optionally set a specific error state for this doctor in the map
        setDoctorsMap(prevMap => ({ ...prevMap, [doctorId]: { name: 'Error loading' } }));
      }
    };

    if (appointments.length > 0) {
      const uniqueDoctorIds = [...new Set(appointments.map(appt => appt.doctor_id).filter(id => !!id))];
      uniqueDoctorIds.forEach(id => fetchDoctorDetails(id));
    }
  }, [appointments, doctorsMap]); // Added doctorsMap to dependencies to avoid potential re-runs if modified elsewhere, though unlikely here.

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value, page: 1 })); // Reset to page 1 on filter change
  };
  
  const handlePatientSearch = async (e) => {
    e.preventDefault();
    if (!patientSearchTerm.trim()) return;
    
    try {
      setLoading(true);
      // Search for patients by name or phone
      const response = await fetch(`/api/appointments?patientSearch=${encodeURIComponent(patientSearchTerm)}`);
      const data = await response.json();
      
      if (response.ok && data.success) {
        setAppointments(data.data.appointments || []);
        setPagination(data.data.pagination || { totalItems: 0, totalPages: 1, currentPage: 1, pageSize: filters.limit });
      } else {
        setError(data.message || 'Failed to search for patients.');
        setAppointments([]);
      }
    } catch (err) {
      console.error('Patient search error:', err);
      setError('An unexpected error occurred while searching for patients.');
      setAppointments([]);
    } finally {
      setLoading(false);
    }
  };

  const handleApplyFilters = () => {
    // Update URL and trigger fetch via useEffect
    const queryParams = { ...filters };
    router.push({ pathname: '/appointments', query: queryParams }, undefined, { shallow: true });
  };
  
  const handleSort = (column) => {
    const newSortOrder = filters.sortBy === column && filters.sortOrder === 'ASC' ? 'DESC' : 'ASC';
    const newFilters = { ...filters, sortBy: column, sortOrder: newSortOrder, page: 1 };
    setFilters(newFilters);
    router.push({ pathname: '/appointments', query: newFilters }, undefined, { shallow: true });
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      const newFilters = { ...filters, page: newPage };
      setFilters(newFilters);
      router.push({ pathname: '/appointments', query: newFilters }, undefined, { shallow: true });
    }
  };

  const renderSortIcon = (column) => {
    if (filters.sortBy !== column) {
      return <ArrowUp className="ml-1 h-4 w-4 text-slate-400" />;
    }
    
    return filters.sortOrder === 'ASC' ? 
      <ArrowUp className="ml-1 h-4 w-4 text-teal-500" /> : 
      <ArrowDown className="ml-1 h-4 w-4 text-teal-500" />;
  };

  return (
    <Layout title="Appointments">
      <div className="container mx-auto px-4 py-6">
        {/* Filters */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-200 mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
            <h2 className="text-lg font-semibold text-slate-800 mb-4 md:mb-0">Appointment Filters</h2>
            <div className="flex space-x-2">
              <button
                onClick={handleApplyFilters}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-[#003253] hover:bg-[#004b7a] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#003253]"
              >
                <Filter className="h-4 w-4 mr-2" aria-hidden="true" />
                Apply Filters
              </button>
            </div>
          </div>
          
          {/* Patient search form */}
          <div className="mb-6 p-4 bg-slate-50 rounded-lg border border-slate-200">
            <h3 className="text-md font-medium text-slate-800 mb-3">Search Patients</h3>
            <form onSubmit={handlePatientSearch} className="flex">
              <input
                type="search"
                value={patientSearchTerm}
                onChange={(e) => setPatientSearchTerm(e.target.value)}
                placeholder="Search by patient name or phone number"
                className="flex-1 shadow-sm focus:ring-[#003253] focus:border-[#003253] block w-full sm:text-sm border-slate-300 rounded-md py-2 px-4"
              />
              <button
                type="submit"
                className="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-[#003253] hover:bg-[#004b7a] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#003253]"
              >
                <Search className="h-4 w-4 mr-2" />
                Search Patients
              </button>
            </form>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-slate-700 mb-1">Start Date</label>
              <input
                type="date"
                id="startDate"
                name="startDate"
                value={filters.startDate}
                onChange={handleFilterChange}
                className="shadow-sm focus:ring-[#003253] focus:border-[#003253] block w-full sm:text-sm border-slate-300 rounded-md"
              />
            </div>
            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-slate-700 mb-1">End Date</label>
              <input
                type="date"
                id="endDate"
                name="endDate"
                value={filters.endDate}
                onChange={handleFilterChange}
                className="shadow-sm focus:ring-[#003253] focus:border-[#003253] block w-full sm:text-sm border-slate-300 rounded-md"
              />
            </div>
            <div>
              <label htmlFor="doctorId" className="block text-sm font-medium text-slate-700 mb-1">Doctor</label>
              <select
                id="doctorId"
                name="doctorId"
                value={filters.doctorId}
                onChange={handleFilterChange}
                className="shadow-sm focus:ring-[#003253] focus:border-[#003253] block w-full sm:text-sm border-slate-300 rounded-md"
              >
                <option value="">All Doctors</option>
                {doctors.map(doctor => (
                  <option key={doctor.id} value={doctor.id}>
                    {doctor.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-slate-700 mb-1">Status</label>
              <select
                id="status"
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                className="shadow-sm focus:ring-[#003253] focus:border-[#003253] block w-full sm:text-sm border-slate-300 rounded-md"
              >
                <option value="">All Statuses</option>
                <option value="scheduled">Scheduled</option>
                <option value="confirmed">Confirmed</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
                <option value="no-show">No Show</option>
              </select>
            </div>
          </div>
        </div>
        
        {/* Appointments Table */}
        <div className="bg-white shadow-sm border border-slate-200 overflow-hidden sm:rounded-lg">
          <div className="px-6 py-5 flex justify-between items-center border-b border-slate-200">
            <div className="flex items-center">
              <Calendar className="h-5 w-5 text-teal-600 mr-2" />
              <h3 className="text-lg leading-6 font-semibold text-slate-800">Appointments</h3>
            </div>
            <p className="text-sm text-slate-500">
              {loading ? 'Loading...' : `Showing ${appointments.length} appointments`}
            </p>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-slate-200">
              <thead className="bg-slate-50">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('patient_name')}
                  >
                    <div className="flex items-center">
                      Patient
                      {renderSortIcon('patient_name')}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('doctor_name')}
                  >
                    <div className="flex items-center">
                      Doctor
                      {renderSortIcon('doctor_name')}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('start_time')}
                  >
                    <div className="flex items-center">
                      Date & Time
                      {renderSortIcon('start_time')}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('status')}
                  >
                    <div className="flex items-center">
                      Status
                      {renderSortIcon('status')}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider"
                  >
                    Notes
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-slate-200">
                {loading ? (
                  <tr><td colSpan="6" className="text-center py-4">Loading appointments...</td></tr>
                ) : error ? (
                  <tr><td colSpan="6" className="text-center py-4 text-red-500">{error}</td></tr>
                ) : appointments.length > 0 ? (
                  appointments.map((appt) => (
                    <tr key={appt.id} className="hover:bg-slate-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-800">{appt.patient_name}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-600">
                        {doctorsMap[appt.doctor_id]?.name || appt.doctor_name || 'Loading...'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-600">
                        {new Date(appt.start_time).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          appt.status === 'scheduled' ? 'bg-teal-100 text-teal-800' :
                          appt.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                          appt.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                          'bg-amber-100 text-amber-800'
                        }`}>
                          {appt.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-600 truncate max-w-xs">{appt.notes}</td>
                    </tr>
                  ))
                ) : (
                  <tr><td colSpan="6" className="text-center py-4 text-slate-500">No appointments found.</td></tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination Controls */}
          {!loading && pagination.totalItems > 0 && (
              <div className="flex items-center justify-between border-t border-slate-200 bg-white px-6 py-4">
                  <div className="flex flex-1 justify-between sm:hidden">
                      <button onClick={() => handlePageChange(pagination.currentPage - 1)} disabled={pagination.currentPage <= 1} className="relative inline-flex items-center rounded-md border border-slate-300 bg-white px-4 py-2 text-sm font-medium text-slate-700 hover:bg-slate-50 disabled:opacity-50">
                          Previous
                      </button>
                      <button onClick={() => handlePageChange(pagination.currentPage + 1)} disabled={pagination.currentPage >= pagination.totalPages} className="relative ml-3 inline-flex items-center rounded-md border border-slate-300 bg-white px-4 py-2 text-sm font-medium text-slate-700 hover:bg-slate-50 disabled:opacity-50">
                          Next
                      </button>
                  </div>
                  <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                      <div>
                          <p className="text-sm text-slate-700">
                          Showing <span className="font-medium">{(pagination.currentPage - 1) * pagination.pageSize + 1}</span>
                          {' '}to <span className="font-medium">{Math.min(pagination.currentPage * pagination.pageSize, pagination.totalItems)}</span>
                          {' '}of <span className="font-medium">{pagination.totalItems}</span> results
                          </p>
                      </div>
                      <div>
                          <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                              <button onClick={() => handlePageChange(pagination.currentPage - 1)} disabled={pagination.currentPage <= 1} className="relative inline-flex items-center rounded-l-md px-2 py-2 text-slate-400 ring-1 ring-inset ring-slate-300 hover:bg-slate-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50">
                                  <span className="sr-only">Previous</span>
                                  <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                              </button>
                              {/* Current page and nearby pages logic can be added here for more complex pagination */}
                              <span aria-current="page" className="relative z-10 inline-flex items-center bg-teal-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-teal-600">
                                {pagination.currentPage}
                              </span>
                              <button onClick={() => handlePageChange(pagination.currentPage + 1)} disabled={pagination.currentPage >= pagination.totalPages} className="relative inline-flex items-center rounded-r-md px-2 py-2 text-slate-400 ring-1 ring-inset ring-slate-300 hover:bg-slate-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50">
                                  <span className="sr-only">Next</span>
                                  <ChevronRight className="h-5 w-5" aria-hidden="true" />
                              </button>
                          </nav>
                      </div>
                  </div>
              </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default AppointmentsPage;
