[{"id": "123", "name": "General Hospital", "address": "123 Main Street, City, State, 12345", "phone": "+19876543210", "email": "<EMAIL>", "sip_trunk": {"provider": "jambonz", "sip_endpoint": "sip.jambonz.org", "auth_token": "sample_token_123"}, "whatsapp": {"provider": "twi<PERSON>", "phone_number": "+14155238886", "webhook_path": "/whatsapp/hospital123"}, "languages": ["en", "hi", "bn"], "db_postgres": "postgresql://hospital_user:hospitaldb@localhost:5432/hospital123", "created_at": "2025-01-01T00:00:00Z", "emergency_number": "911", "settings": {"timezone": "America/New_York", "working_hours": {"monday": "08:00-20:00", "tuesday": "08:00-20:00", "wednesday": "08:00-20:00", "thursday": "08:00-20:00", "friday": "08:00-20:00", "saturday": "09:00-17:00", "sunday": "09:00-17:00"}, "appointment_duration_minutes": 30, "logo_url": "https://example.com/hospital_logo.png", "services": ["voice", "whatsapp"]}}, {"id": "456", "name": "City Medical Center", "address": "456 Health Avenue, Metropolis, State, 67890", "phone": "+18765432109", "email": "<EMAIL>", "sip_trunk": {"provider": "jambonz", "sip_endpoint": "sip.jambonz.org", "auth_token": "sample_token_456"}, "whatsapp": {"provider": "twi<PERSON>", "phone_number": "+14155238887", "webhook_path": "/whatsapp/hospital456"}, "languages": ["en", "es", "zh"], "db_postgres": "postgresql://hospital_user:hospitaldb@localhost:5432/hospital456", "created_at": "2025-01-01T00:00:00Z", "emergency_number": "911", "settings": {"timezone": "America/Los_Angeles", "services": ["voice", "whatsapp"], "working_hours": {"monday": "07:00-19:00", "tuesday": "07:00-19:00", "wednesday": "07:00-19:00", "thursday": "07:00-19:00", "friday": "07:00-19:00", "saturday": "08:00-16:00", "sunday": "08:00-16:00"}, "appointment_duration_minutes": 45, "logo_url": "https://example.com/citymedical_logo.png"}}, {"id": "459", "name": "Community Health Clinic", "address": "789 Wellness Road, Smalltown, State, 54321", "phone": "+17654321098", "email": "<EMAIL>", "sip_trunk": {"provider": "jambonz", "sip_endpoint": "sip.jambonz.org", "auth_token": "sample_token_789"}, "languages": ["en", "fr", "ar"], "db_postgres": "postgresql://hospital_user:hospitaldb@localhost:5432/hospital459", "ssh_tunnel": {"user": "ssh_user", "host": "ssh.communityclinic.org", "private_key_path": "/path/to/private_key.pem"}, "created_at": "2025-01-01T00:00:00Z", "emergency_number": "911", "settings": {"timezone": "America/Chicago", "services": ["voice"], "working_hours": {"monday": "09:00-17:00", "tuesday": "09:00-17:00", "wednesday": "09:00-17:00", "thursday": "09:00-17:00", "friday": "09:00-17:00", "saturday": "10:00-14:00", "sunday": "closed"}, "appointment_duration_minutes": 30, "logo_url": "https://example.com/community_logo.png"}}]