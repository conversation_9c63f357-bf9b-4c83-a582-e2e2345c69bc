The company name is Ascle AI and voice agent is MEGHA
# Hospital Voice Agent and Staff Portal System: Development Roadmap

This system serves hospitals by providing a voice agent (Megha) for patients to book appointments and tests via phone, and a staff portal for hospital staff to manage operations online. It must scale to multiple hospitals, each with its own data, SIP provider, and server. The staff portal must be **completely dynamic**, adapting to each hospital’s data and user roles, and every hospital will have a **unique PostgreSQL database**. Follow these phases to build the system step by step.

---

## Phase 1: Project Overview and Environment Setup

### Objectives
- **Voice Agent (Megha)**: Handles patient calls, books appointments/tests, and adapts to each hospital’s language and configuration.
- **Staff Portal**: A web app for staff to manage schedules, bookings, and communications, dynamically tailored to each hospital and user role.
- **Databases**:
  - **Firebase (Firestore)**: Stores doctor schedules, test info, and staff details in the cloud.
  - **PostgreSQL**: A unique instance per hospital, hosted on their server, for bookings and call recordings.
- **Security**: SSH tunnels for database access, JWT for staff authentication.
- **Deployment**: Voice agent on Fly.io, staff portal on Hostinger.

### Setup
- Install tools: 
  - **Node.js** (for staff portal)
  - **Python** (for voice agent)
  - **Firebase CLI** (for Firestore)
  - **Fly.io CLI** (for deployment)
  - **SSH client** (for tunnels)
- Plan database schemas to ensure they support dynamic, hospital-specific data.

---

## Phase 2: Database Design and Implementation

### 2.1 Firebase (Firestore) - Shared Cloud Storage
- **Collection**: `hospitals`
  - **Document**: `hospital_<unique_id>` (e.g., `hospital_001`)
    ```json
    {
      "name": "City Hospital",
      "sip_trunk": {
        "provider": "Jio",
        "sip_endpoint": "sip.jio.com",
        "auth_token": "xxxxxx"
      },
      "languages": ["en", "hi"],
      "db_postgres": "postgresql://user:pass@localhost:5432/hospital_001",
      "ssh_tunnel": {
        "user": "ssh_user",
        "host": "hospital-server-ip",
        "private_key_path": "/path/to/private_key"
      },
      "created_at": "2025-04-04"
    }
    ```
- **Collection**: `hospital_<unique_id>_data` (e.g., `hospital_001_data`)
  - **Subcollections**:
    - `doctors`: `{id, name, specialty, schedule, availability, price}`
    - `test_info`: `{id, name, description, duration, cost, requirements}`
    - `staff`: `{id, name, role, credentials: {userid, password}, contact}`
    - `settings`: `{reminder_system_enabled}`

### 2.2 PostgreSQL - Unique Per Hospital
- **Database**: `hospital_<unique_id>` (e.g., `hospital_001`), hosted on each hospital’s server.
- **Tables**:
  - `appointments`:
    ```sql
    CREATE TABLE appointments (
      id SERIAL PRIMARY KEY,
      patient_name VARCHAR(255) NOT NULL,
      phone VARCHAR(15) NOT NULL,
      doctor_id VARCHAR(50) NOT NULL,
      time TIMESTAMP NOT NULL,
      status VARCHAR(20) CHECK (status IN ('confirmed', 'cancelled', 'completed')),
      created_at DATE NOT NULL
    );
    ```
  - `test_bookings`:
    ```sql
    CREATE TABLE test_bookings (
      id SERIAL PRIMARY KEY,
      patient_name VARCHAR(255) NOT NULL,
      phone VARCHAR(15) NOT NULL,
      test_type_id VARCHAR(50) NOT NULL,
      time TIMESTAMP NOT NULL,
      status VARCHAR(20) CHECK (status IN ('scheduled', 'cancelled', 'completed')),
      result_url VARCHAR(255),
      created_at DATE NOT NULL
    );
    ```
  - `calls`:
    ```sql
    CREATE TABLE calls (
      id SERIAL PRIMARY KEY,
      caller_number VARCHAR(15) NOT NULL,
      timestamp TIMESTAMP NOT NULL,
      duration INTEGER NOT NULL,
      recording BYTEA NOT NULL,
      hospital_id VARCHAR(50) NOT NULL
    );
    ```

### 2.3 Secure Access
- Use **SSH tunnels** to connect to each hospital’s PostgreSQL instance, with configs stored in Firestore (`ssh_tunnel`).

---

## Phase 3: Voice Agent (Megha) Development

### Core Features
- **Framework**: Build with **FastAPI** for async call handling.
- **Telephony**: Use **Jambonz** to route SIP calls to FastAPI endpoints, dynamically based on hospital DID.
- **Interaction**:
  - Natural language with **GPT-4o Mini**.
  - Text-to-speech via **Google Cloud TTS**, using hospital-specific languages.
  - Store call context in **Redis** with a 10-minute TTL also implement proper caching in redis.
  - Fine tune 4o mini model to deliver only medical field related answers
  - Implement natural language processing, fuzzy matching 
  - implement a system which is very easy for the caller to understand 
  - The agent should give option between hindi, English and bengali languages for the user to choose.
- **Call Flow**:
  - Welcome: “Welcome to [Hospital Name]. Press 0 for emergencies, or select a language. Your all is being recorded.”
  - Record calls and store in PostgreSQL `calls`.
  - Use caller’s number for bookings and SMS.
- **Booking**:
  - Check doctor/test availability in Firestore(keep it dynamic completely).
  - Save to PostgreSQL via SSH tunnel.
  - Send SMS confirmation.

### Dynamic Behavior
- Pull hospital config (SIP, languages) from Firestore.
- Route data to the correct PostgreSQL instance using `hospital_id`.

---

## Phase 4: Staff Portal Development

The staff portal must be **fully dynamic**, adapting to each hospital’s data and user roles without hardcoding.

### 4.1 Tech Stack
- **Next.js** + **React**: For frontend and server-side logic.
- **Tailwind CSS**: For styling.

### 4.2 Features
- **Landing Page** (`/`): Simple “Sign In” button to `/login`.
- **Login Page** (`/login`):
  - Validate `userid` and `password` against Firestore `staff`.
  - Issue **JWT** stored in secure cookies.
  - Implement proper session management and authorisation
- **Dashboard** (`/dashboard`):
  - **Dynamic Display**: Show sections for each doctor with today’s appointments from PostgreSQL, filtered by `hospital_id` in JWT.
  - **Search**: Query Firestore (`doctors`, `staff`) and PostgreSQL (`appointments`, `test_bookings`) for hospital-specific results.
- **Reminders**:
  - Button per doctor: “Send Reminder” → “Message or Call?” → Notify all patients for that doctor.
  - Send notification in the staff portal at the time of any doctor arrival asking whther doctor arrived or not if not then ask how late(eg, 15mins, 30mins) then send late notification message to all patients of that particular doctor only.
- **Admin Page** (`/admin`):
  - Add/remove staff, update schedules, toggle reminders—all synced to Firestore.
  - Manual sync button for databases.

### 4.3 Security
- **JWT**: Restrict data access to user’s `hospital_id`.
- **Passwords**: Hash with `bcrypt`.

---

## Phase 5: Integration and Optimization

- **Database Routing**: Dynamically query Firestore or the correct PostgreSQL instance based on `hospital_id`.
- **Sync**: Auto-sync Firestore, PostgreSQL, and Redis on updates; manual sync via admin.
- **Performance**: Cache frequent queries in Redis, use connection pooling.
- **Abuse Prevention**: Rate-limit APIs, validate inputs.
- **Cost optimisation**: Implement cost saving techniques such that llm bill is not too high 

---

## Phase 6: Testing
- Mentally simulate or runtime-test:
  - Multi-hospital scenarios.
  - High call volumes.
  - Edge cases (e.g., unavailable doctors, invalid inputs).
- Verify all components: telephony, databases, portal, security.

---

## Phase 7: Deployment and Documentation
- **Deploy**:
  - Voice agent to **Fly.io**.
  - Staff portal to **Hostinger**.
- **Documentation**:
  - Setup guide for tools and SSH tunnels.
  - Instructions for adding new hospitals and PostgreSQL instances.
  - User manual for staff and admins.
  -How to test the system locally in pc, how to add new hospitals/staff members with unique sip details, postgresSQL URL.

---

## Key Principles
- **Dynamic Staff Portal**: Tailor all data and features to the user’s hospital and role.
- **Unique PostgreSQL**: Each hospital has its own database, accessed securely.
- **Seamless Integration**: Ensure Megha and the portal work flawlessly with both databases.