{"name": "whatsapp-agent", "version": "1.0.0", "description": "WhatsApp agent for Hospital Management System", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@google-cloud/vision": "^4.0.0", "axios": "^1.6.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "firebase-admin": "^11.11.0", "openai": "^4.20.0", "pg": "^8.11.3", "redis": "^4.6.10", "twilio": "^4.19.0", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}