/**
 * Database module for WhatsApp Agent
 * 
 * Handles PostgreSQL database connections and operations for hospital-specific databases
 */

import pg from 'pg';
import { logger } from './logger.js';
import { getHospitalConfig, getHospitalDbConnection } from './hospitals.js';
import firebase from './firebase.js';
import { getRedisClient } from './redis.js';
import { generateAppointmentId } from './utils.js';

// Map to store database connection pools by hospital ID
const dbPools = new Map();

/**
 * Get or create a database connection pool for a specific hospital
 * @param {string} hospitalId - Hospital ID
 * @returns {pg.Pool} - PostgreSQL connection pool
 */
export const getDbPool = (hospitalId) => {
  // Check if pool already exists
  if (dbPools.has(hospitalId)) {
    return dbPools.get(hospitalId);
  }
  
  // Get connection string from hospital config
  const connectionString = getHospitalDbConnection(hospitalId);
  if (!connectionString) {
    throw new Error(`Database connection string not found for hospital ${hospitalId}`);
  }
  
  // Create new connection pool
  const pool = new pg.Pool({
    connectionString,
    max: 20, // Maximum number of clients in the pool
    idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
    connectionTimeoutMillis: 2000 // Return an error after 2 seconds if connection could not be established
  });
  
  // Set up error handler
  pool.on('error', (err) => {
    logger.error(`Unexpected error on idle client for hospital ${hospitalId}:`, err);
  });
  
  // Store pool in map
  dbPools.set(hospitalId, pool);
  logger.info(`Created database connection pool for hospital ${hospitalId}`);
  
  return pool;
};

/**
 * Close all database connection pools
 */
export const closeAllPools = async () => {
  const closingPromises = [];
  
  for (const [hospitalId, pool] of dbPools.entries()) {
    logger.info(`Closing database connection pool for hospital ${hospitalId}`);
    closingPromises.push(pool.end());
  }
  
  await Promise.all(closingPromises);
  dbPools.clear();
  logger.info('All database connection pools closed');
};

/**
 * Save WhatsApp message to database
 * @param {Object} message - WhatsApp message object
 * @returns {Promise<Object>} - Saved message record
 */
export const saveWhatsAppMessage = async (message) => {
  const { hospitalId, messageId, phoneNumber, direction, messageText, mediaUrl, timestamp, metadata } = message;
  
  try {
    const pool = getDbPool(hospitalId);
    
    const query = `
      INSERT INTO whatsapp_chat_history (
        message_id, phone_number, hospital_id, direction, message_text, 
        media_url, timestamp, metadata
      ) 
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;
    
    const values = [
      messageId,
      phoneNumber,
      hospitalId,
      direction,
      messageText,
      mediaUrl || null,
      timestamp || new Date(),
      metadata ? JSON.stringify(metadata) : '{}'
    ];
    
    const result = await pool.query(query, values);
    logger.info(`Saved WhatsApp message ${messageId} to database`);
    
    return result.rows[0];
  } catch (error) {
    logger.error(`Error saving WhatsApp message to database:`, error);
    throw error;
  }
};

/**
 * Get WhatsApp chat history for a specific phone number and hospital
 * @param {string} hospitalId - Hospital ID
 * @param {string} phoneNumber - Customer phone number
 * @param {number} limit - Maximum number of messages to retrieve
 * @returns {Promise<Array>} - Chat history records
 */
export const getWhatsAppChatHistory = async (hospitalId, phoneNumber, limit = 50) => {
  try {
    const pool = getDbPool(hospitalId);
    
    const query = `
      SELECT * FROM whatsapp_chat_history
      WHERE hospital_id = $1 AND phone_number = $2
      ORDER BY timestamp DESC
      LIMIT $3
    `;
    
    const values = [hospitalId, phoneNumber, limit];
    
    const result = await pool.query(query, values);
    return result.rows;
  } catch (error) {
    logger.error(`Error retrieving WhatsApp chat history:`, error);
    throw error;
  }
};

/**
 * Create a new appointment from WhatsApp booking
 * @param {Object} appointment - Appointment data
 * @returns {Promise<Object>} - Created appointment record
 */
export const createAppointment = async (appointment) => {
  const { 
    patientId, doctorId, hospitalId, 
    startTime, endTime, status, notes, phoneNumber 
  } = appointment;
  
  try {
    const pool = getDbPool(hospitalId);
    
    // Generate a 6-digit appointment ID
    const appointmentId = generateAppointmentId(phoneNumber || patientId);
    
    // Start a transaction to ensure data consistency
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Insert appointment
      const query = `
        INSERT INTO appointments (
          id, patient_id, doctor_id, hospital_id,
          start_time, end_time, status, source, notes
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *
      `;
      
      const values = [
        appointmentId,
        patientId,
        doctorId,
        hospitalId,
        startTime,
        endTime,
        status || 'scheduled',
        'whatsapp',
        notes || null
      ];
      
      const result = await client.query(query, values);
      logger.info(`Created appointment ${id} in database`);
      
      // Get the appointment date for booking count tracking
      const appointmentDate = new Date(startTime).toISOString().split('T')[0];
      
      // Update booking count in Redis for real-time tracking
      await updateBookingCountInRedis(hospitalId, doctorId, appointmentDate);
      
      // Commit the transaction
      await client.query('COMMIT');
      
      // After successful database transaction, update Firebase asynchronously
      // We don't want to block the appointment creation if Firebase update fails
      updateBookingCountInFirebase(hospitalId, doctorId, appointmentDate).catch(err => {
        logger.error(`Error updating booking count in Firebase: ${err.message}`);
      });
      
      return result.rows[0];
    } catch (err) {
      // Rollback transaction on error
      await client.query('ROLLBACK');
      throw err;
    } finally {
      // Release client back to pool
      client.release();
    }
  } catch (error) {
    logger.error(`Error creating appointment in database:`, error);
    throw error;
  }
};

/**
 * Update booking count in Redis for real-time tracking
 * @param {string} hospitalId - Hospital ID
 * @param {string} doctorId - Doctor ID
 * @param {string} date - Appointment date (YYYY-MM-DD)
 * @returns {Promise<void>}
 */
async function updateBookingCountInRedis(hospitalId, doctorId, date) {
  try {
    // Get current booking count
    const currentCount = await getDoctorBookingCount(hospitalId, doctorId, date);
    
    // Set the count in Redis
    const redisClient = redis.getRedisClient();
    const key = `hospital:${hospitalId}:doctor:${doctorId}:bookings:${date}`;
    await redisClient.set(key, currentCount.toString());
    
    // Set expiration for the key (keep for 7 days)
    await redisClient.expire(key, 60 * 60 * 24 * 7);
    
    logger.info(`Updated booking count in Redis for doctor ${doctorId} on ${date}: ${currentCount}`);
  } catch (error) {
    logger.error(`Error updating booking count in Redis:`, error);
    throw error;
  }
}

/**
 * Update booking count in Firebase for cross-service tracking
 * @param {string} hospitalId - Hospital ID
 * @param {string} doctorId - Doctor ID
 * @param {string} date - Appointment date (YYYY-MM-DD)
 * @returns {Promise<void>}
 */
async function updateBookingCountInFirebase(hospitalId, doctorId, date) {
  try {
    // Get current booking count
    const currentCount = await getDoctorBookingCount(hospitalId, doctorId, date);
    
    // Get doctor data from Firebase
    const doctor = await firebase.getDoctor(hospitalId, doctorId);
    if (!doctor) {
      throw new Error(`Doctor ${doctorId} not found in Firebase`);
    }
    
    // Update the doctor's current_bookings field
    // This creates/updates a map of date -> count
    if (!doctor.current_bookings) {
      doctor.current_bookings = {};
    }
    
    doctor.current_bookings[date] = currentCount;
    
    // Update doctor in Firebase
    const db = firebase.getFirestore();
    const doctorRef = db.collection(`hospital_${hospitalId}_data`)
                        .doc('doctors')
                        .collection('doctors')
                        .doc(doctorId);
    
    await doctorRef.update({
      current_bookings: doctor.current_bookings,
      updated_at: new Date()
    });
    
    logger.info(`Updated booking count in Firebase for doctor ${doctorId} on ${date}: ${currentCount}`);
  } catch (error) {
    logger.error(`Error updating booking count in Firebase:`, error);
    throw error;
  }
};

/**
 * Get doctor's booking count for a specific date
 * @param {string} hospitalId - Hospital ID
 * @param {string} doctorId - Doctor ID
 * @param {string} date - Date in YYYY-MM-DD format
 * @returns {Promise<number>} - Number of bookings
 */
export const getDoctorBookingCount = async (hospitalId, doctorId, date) => {
  try {
    // First try to get count from Redis for performance
    try {
      const redisClient = redis.getRedisClient();
      const key = `hospital:${hospitalId}:doctor:${doctorId}:bookings:${date}`;
      const cachedCount = await redisClient.get(key);
      
      if (cachedCount !== null) {
        return parseInt(cachedCount, 10);
      }
    } catch (redisError) {
      // If Redis fails, continue with database query
      logger.warn(`Redis error when getting booking count, falling back to database: ${redisError.message}`);
    }
    
    // Get count from database
    const pool = getDbPool(hospitalId);
    
    // Convert date string to start and end of day
    const startOfDay = new Date(`${date}T00:00:00`);
    const endOfDay = new Date(`${date}T23:59:59`);
    
    const query = `
      SELECT COUNT(*) as booking_count
      FROM appointments
      WHERE hospital_id = $1 
        AND doctor_id = $2
        AND start_time >= $3
        AND start_time <= $4
        AND status != 'cancelled'
    `;
    
    const values = [hospitalId, doctorId, startOfDay, endOfDay];
    
    const result = await pool.query(query, values);
    const count = parseInt(result.rows[0].booking_count, 10);
    
    // Cache the result in Redis
    try {
      const redisClient = redis.getRedisClient();
      const key = `hospital:${hospitalId}:doctor:${doctorId}:bookings:${date}`;
      await redisClient.set(key, count.toString());
      await redisClient.expire(key, 60 * 60 * 24); // Expire after 24 hours
    } catch (redisError) {
      // If Redis fails, just log the error but continue
      logger.warn(`Redis error when caching booking count: ${redisError.message}`);
    }
    
    return count;
  } catch (error) {
    logger.error(`Error getting doctor booking count:`, error);
    throw error;
  }
};

/**
 * Create a new patient record if it doesn't exist
 * @param {Object} patient - Patient data
 * @returns {Promise<Object>} - Patient record
 */
export const createOrUpdatePatient = async (patient) => {
  const { id, name, phone, email, hospitalId } = patient;
  
  try {
    const pool = getDbPool(hospitalId);
    
    // Check if patient exists
    const checkQuery = `
      SELECT * FROM patients
      WHERE phone = $1 AND hospital_id = $2
    `;
    
    const checkResult = await pool.query(checkQuery, [phone, hospitalId]);
    
    if (checkResult.rows.length > 0) {
      // Patient exists, update if needed
      const existingPatient = checkResult.rows[0];
      
      // Only update if there are changes
      if (name !== existingPatient.name || (email && email !== existingPatient.email)) {
        const updateQuery = `
          UPDATE patients
          SET name = $1, email = $2, updated_at = NOW()
          WHERE id = $3
          RETURNING *
        `;
        
        const updateResult = await pool.query(updateQuery, [
          name, 
          email || existingPatient.email, 
          existingPatient.id
        ]);
        
        logger.info(`Updated patient ${existingPatient.id} in database`);
        return updateResult.rows[0];
      }
      
      return existingPatient;
    } else {
      // Create new patient
      const insertQuery = `
        INSERT INTO patients (
          id, name, phone, email, hospital_id
        )
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `;
      
      const insertResult = await pool.query(insertQuery, [
        id,
        name,
        phone,
        email || null,
        hospitalId
      ]);
      
      logger.info(`Created patient ${id} in database`);
      return insertResult.rows[0];
    }
  } catch (error) {
    logger.error(`Error creating/updating patient in database:`, error);
    throw error;
  }
};

/**
 * Create a new test booking from WhatsApp
 * @param {Object} booking - Test booking data
 * @returns {Promise<Object>} - Created test booking record
 */
export const createTestBooking = async (booking) => {
  const { 
    patientName, patientId, testId, hospitalId, 
    bookingTime, status, notes, phoneNumber 
  } = booking;
  
  try {
    const pool = getDbPool(hospitalId);
    
    // Generate a 6-digit appointment ID
    const bookingId = generateAppointmentId(phoneNumber || patientId);
    
    // Start a transaction to ensure data consistency
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Insert test booking
      const query = `
        INSERT INTO test_bookings (
          id, patient_name, phone, test_type_id, hospital_id, 
          booking_time, status, source, notes
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *
      `;
      
      const values = [
        bookingId,
        patientName,
        patientId,
        testId,
        hospitalId,
        bookingTime,
        status || 'scheduled',
        'whatsapp',
        notes || null
      ];
      
      const result = await client.query(query, values);
      logger.info(`Created test booking ${bookingId} in database`);
      
      // TODO: Update test booking counts in Firebase and Redis if needed
      
      // Commit transaction
      await client.query('COMMIT');
      
      return result.rows[0];
    } catch (err) {
      // Rollback transaction on error
      await client.query('ROLLBACK');
      throw err;
    } finally {
      // Release client back to pool
      client.release();
    }
  } catch (error) {
    logger.error(`Error creating test booking in database:`, error);
    throw error;
  }
};

export default {
  getDbPool,
  closeAllPools,
  saveWhatsAppMessage,
  getWhatsAppChatHistory,
  createAppointment,
  getDoctorBookingCount,
  createTestBooking,
  createOrUpdatePatient,
  updateBookingCountInRedis,
  updateBookingCountInFirebase
};
