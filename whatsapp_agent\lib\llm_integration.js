/**
 * LLM Integration for WhatsApp Agent
 * 
 * This module integrates the unified LLM service with the WhatsApp agent,
 * replacing the previous AI module with function calling approach.
 */

import { logger } from './logger.js';
import { getDoctors, getTests, checkDoctorBookingLimit } from './firebase.js';

// Bridge to communicate with the Python LLM service via HTTP API
class WhatsAppLLMIntegration {
  constructor() {
    this.llmServiceUrl = process.env.LLM_SERVICE_URL || 'http://localhost:8001';
    this.model = 'gpt-4o-mini'; // GPT-4.1 nano
    this.timeout = 30000; // 30 seconds timeout
  }

  /**
   * Call the Python LLM service via HTTP API
   * @param {string} message - Message to process
   * @param {Object} context - Context object
   * @returns {Promise<Object>} - LLM service response
   */
  async callLLMService(message, context) {
    try {
      const response = await fetch(`${this.llmServiceUrl}/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: message,
          context: context
        }),
        signal: AbortSignal.timeout(this.timeout)
      });

      if (!response.ok) {
        throw new Error(`LLM service responded with status: ${response.status}`);
      }

      const result = await response.json();
      return result;

    } catch (error) {
      logger.error('Error calling LLM service:', error);
      throw error;
    }
  }

  /**
   * Process WhatsApp message using LLM with function calling
   * @param {Object} message - Message object
   * @param {Object} hospitalContext - Hospital context
   * @returns {Promise<Object>} - Processed result
   */
  async processMessage(message, hospitalContext) {
    try {
      const { text, phoneNumber, language, chatHistory, ocrContext } = message;
      const { hospitalId, hospitalName, doctors, tests } = hospitalContext;

      // Prepare context for LLM
      const context = {
        hospital_id: hospitalId,
        hospital_name: hospitalName,
        language: language || 'en',
        user_info: {
          phone: phoneNumber
        },
        chat_history: this._formatChatHistory(chatHistory),
        available_doctors: doctors,
        available_tests: tests,
        ocr_context: ocrContext
      };

      // Use the unified LLM service with fallback to local logic
      const result = await this._processWithLLMService(text, context);

      return {
        originalText: text,
        response: result.response,
        bookingDetails: result.booking_details,
        intent: result.intent,
        language: result.detected_language || language || 'en',
        functionCalls: result.function_calls || []
      };

    } catch (error) {
      logger.error('Error processing message with LLM:', error);
      throw error;
    }
  }

  /**
   * Process message using the unified LLM service
   * @param {string} text - Input text
   * @param {Object} context - Context object
   * @returns {Promise<Object>} - Processing result
   */
  async _processWithLLMService(text, context) {
    try {
      // Try to call the Python LLM service first
      const llmResult = await this.callLLMService(text, context);

      if (llmResult.success) {
        return {
          response: llmResult.response,
          booking_details: this._extractBookingFromFunctionCalls(llmResult.function_calls),
          intent: this._determineIntentFromFunctionCalls(llmResult.function_calls),
          detected_language: context.language,
          function_calls: llmResult.function_calls
        };
      } else {
        // Fallback to local logic if LLM service fails
        logger.warn('LLM service failed, falling back to local logic');
        return await this._processWithLocalLogic(text, context);
      }

    } catch (error) {
      logger.error('Error with LLM service, falling back to local logic:', error);
      return await this._processWithLocalLogic(text, context);
    }
  }

  /**
   * Process message with local logic (fallback when LLM service is unavailable)
   * @param {string} text - Input text
   * @param {Object} context - Context object
   * @returns {Promise<Object>} - Processing result
   */
  async _processWithLocalLogic(text, context) {
    const textLower = text.toLowerCase();
    const result = {
      response: '',
      booking_details: null,
      intent: 'unknown',
      detected_language: context.language,
      function_calls: []
    };

    try {
      // Detect intent and call appropriate functions
      if (this._isBookingIntent(textLower)) {
        result.intent = 'booking_request';
        
        // Extract booking details
        const bookingDetails = this._extractBookingDetails(text, context);
        
        if (bookingDetails.doctor_name || bookingDetails.doctor_id) {
          // Doctor appointment booking
          const doctorsResult = await this._callGetDoctors(context.hospital_id);
          result.function_calls.push({
            name: 'get_available_doctors',
            arguments: { hospital_id: context.hospital_id },
            result: doctorsResult
          });
          
          if (bookingDetails.patient_name && bookingDetails.doctor_id) {
            // Attempt to book
            const bookingResult = await this._callBookAppointment(bookingDetails, context);
            result.function_calls.push({
              name: 'book_appointment',
              arguments: bookingDetails,
              result: bookingResult
            });
            
            if (bookingResult.success) {
              result.response = `Great! I've booked your appointment with Dr. ${bookingDetails.doctor_name || 'the doctor'} for ${bookingDetails.date} at ${bookingDetails.time}. You'll receive a confirmation SMS shortly.`;
              result.booking_details = bookingDetails;
            } else {
              result.response = `I'm sorry, I couldn't book the appointment. ${bookingResult.error || 'Please try again or contact the hospital directly.'}`;
            }
          } else {
            result.response = this._generateDoctorListResponse(doctorsResult.doctors, context.language);
          }
          
        } else if (bookingDetails.test_name || bookingDetails.test_id) {
          // Test booking
          const testsResult = await this._callGetTests(context.hospital_id);
          result.function_calls.push({
            name: 'get_available_tests',
            arguments: { hospital_id: context.hospital_id },
            result: testsResult
          });
          
          if (bookingDetails.patient_name && bookingDetails.test_id) {
            // Attempt to book test
            const bookingResult = await this._callBookTest(bookingDetails, context);
            result.function_calls.push({
              name: 'book_test',
              arguments: bookingDetails,
              result: bookingResult
            });
            
            if (bookingResult.success) {
              result.response = `Perfect! I've booked your ${bookingDetails.test_name || 'test'} for ${bookingDetails.date} at ${bookingDetails.time}. You'll receive a confirmation SMS with details.`;
              result.booking_details = bookingDetails;
            } else {
              result.response = `I'm sorry, I couldn't book the test. ${bookingResult.error || 'Please try again or contact the hospital directly.'}`;
            }
          } else {
            result.response = this._generateTestListResponse(testsResult.tests, context.language);
          }
        }
        
      } else if (this._isDoctorInquiry(textLower)) {
        result.intent = 'doctor_inquiry';
        const doctorsResult = await this._callGetDoctors(context.hospital_id);
        result.function_calls.push({
          name: 'get_available_doctors',
          arguments: { hospital_id: context.hospital_id },
          result: doctorsResult
        });
        result.response = this._generateDoctorListResponse(doctorsResult.doctors, context.language);
        
      } else if (this._isTestInquiry(textLower)) {
        result.intent = 'test_inquiry';
        const testsResult = await this._callGetTests(context.hospital_id);
        result.function_calls.push({
          name: 'get_available_tests',
          arguments: { hospital_id: context.hospital_id },
          result: testsResult
        });
        result.response = this._generateTestListResponse(testsResult.tests, context.language);
        
      } else {
        result.intent = 'general_query';
        result.response = this._generateGeneralResponse(text, context);
      }

    } catch (error) {
      logger.error('Error in local logic processing:', error);
      result.response = "I'm sorry, I'm experiencing technical difficulties. Please try again later.";
    }

    return result;
  }

  /**
   * Check if text indicates booking intent
   */
  _isBookingIntent(text) {
    const bookingKeywords = ['book', 'appointment', 'schedule', 'reserve', 'test', 'booking'];
    return bookingKeywords.some(keyword => text.includes(keyword));
  }

  /**
   * Check if text is doctor inquiry
   */
  _isDoctorInquiry(text) {
    const doctorKeywords = ['doctor', 'physician', 'specialist', 'dr.', 'dr '];
    return doctorKeywords.some(keyword => text.includes(keyword));
  }

  /**
   * Check if text is test inquiry
   */
  _isTestInquiry(text) {
    const testKeywords = ['test', 'lab', 'blood', 'scan', 'x-ray', 'mri', 'ct'];
    return testKeywords.some(keyword => text.includes(keyword));
  }

  /**
   * Extract booking details from text
   */
  _extractBookingDetails(text, context) {
    // This is a simplified extraction - in a real implementation,
    // this would use the LLM's NER capabilities
    return {
      patient_name: null,
      doctor_id: null,
      doctor_name: null,
      test_id: null,
      test_name: null,
      date: null,
      time: null,
      phone: context.user_info.phone
    };
  }

  /**
   * Call get doctors function
   */
  async _callGetDoctors(hospitalId) {
    try {
      const doctors = await getDoctors(hospitalId);
      return {
        success: true,
        hospital_id: hospitalId,
        doctors: doctors,
        count: doctors.length
      };
    } catch (error) {
      logger.error('Error getting doctors:', error);
      return {
        success: false,
        error: error.message,
        hospital_id: hospitalId
      };
    }
  }

  /**
   * Call get tests function
   */
  async _callGetTests(hospitalId) {
    try {
      const tests = await getTests(hospitalId);
      return {
        success: true,
        hospital_id: hospitalId,
        tests: tests,
        count: tests.length
      };
    } catch (error) {
      logger.error('Error getting tests:', error);
      return {
        success: false,
        error: error.message,
        hospital_id: hospitalId
      };
    }
  }

  /**
   * Call book appointment function (placeholder)
   */
  async _callBookAppointment(bookingDetails, context) {
    // This would integrate with the actual booking system
    return {
      success: false,
      error: "Booking functionality not yet implemented in WhatsApp agent"
    };
  }

  /**
   * Call book test function (placeholder)
   */
  async _callBookTest(bookingDetails, context) {
    // This would integrate with the actual booking system
    return {
      success: false,
      error: "Test booking functionality not yet implemented in WhatsApp agent"
    };
  }

  /**
   * Generate doctor list response
   */
  _generateDoctorListResponse(doctors, language) {
    if (!doctors || doctors.length === 0) {
      return "I'm sorry, no doctors are currently available.";
    }

    let response = "Here are our available doctors:\n\n";
    doctors.forEach((doctor, index) => {
      response += `${index + 1}. Dr. ${doctor.name} - ${doctor.specialty || 'General Medicine'}\n`;
    });
    response += "\nWould you like to book an appointment with any of these doctors?";
    
    return response;
  }

  /**
   * Generate test list response
   */
  _generateTestListResponse(tests, language) {
    if (!tests || tests.length === 0) {
      return "I'm sorry, no tests are currently available.";
    }

    let response = "Here are our available tests:\n\n";
    tests.forEach((test, index) => {
      response += `${index + 1}. ${test.name}`;
      if (test.price) response += ` - ${test.price}`;
      response += '\n';
    });
    response += "\nWould you like to book any of these tests?";
    
    return response;
  }

  /**
   * Generate general response
   */
  _generateGeneralResponse(text, context) {
    return `Hello! I'm here to help you with appointments and medical tests at ${context.hospital_name}. You can ask me about available doctors, tests, or book an appointment. How can I assist you today?`;
  }

  /**
   * Extract booking details from function calls
   */
  _extractBookingFromFunctionCalls(functionCalls) {
    if (!functionCalls || !Array.isArray(functionCalls)) {
      return null;
    }

    for (const call of functionCalls) {
      if (call.name === 'book_appointment' || call.name === 'book_test') {
        return call.arguments;
      }
    }

    return null;
  }

  /**
   * Determine intent from function calls
   */
  _determineIntentFromFunctionCalls(functionCalls) {
    if (!functionCalls || !Array.isArray(functionCalls)) {
      return 'general_query';
    }

    for (const call of functionCalls) {
      switch (call.name) {
        case 'book_appointment':
          return 'booking_request';
        case 'book_test':
          return 'test_booking_request';
        case 'get_available_doctors':
          return 'doctor_inquiry';
        case 'get_available_tests':
          return 'test_inquiry';
        case 'get_hospital_info':
          return 'hospital_inquiry';
        default:
          continue;
      }
    }

    return 'general_query';
  }

  /**
   * Format chat history
   */
  _formatChatHistory(chatHistory) {
    if (!chatHistory || !Array.isArray(chatHistory)) {
      return [];
    }

    return chatHistory.slice(-10).map(msg => ({
      role: msg.direction === 'inbound' ? 'user' : 'assistant',
      content: msg.message_text
    }));
  }
}

// Create global instance
const whatsappLLMIntegration = new WhatsAppLLMIntegration();

export { whatsappLLMIntegration };
export default whatsappLLMIntegration;
