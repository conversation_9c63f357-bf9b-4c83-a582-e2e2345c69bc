import { withAuth } from '../../lib/auth';
import { getHospitalDbPool } from '../../lib/pg_utils';
import { logger } from '../../lib/logger';

export default withAuth(async (req, res) => {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }

  const hospitalId = req.user?.hospital_id;
  if (!hospitalId) {
    return res.status(401).json({ success: false, message: 'User not authenticated or hospital ID missing.' });
  }

  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const offset = (page - 1) * limit;

  const { 
    startDate, 
    endDate, 
    status,
    doctorId, // Assuming this is doctors.id
    patientId, // Assuming this is patients.id
    patientSearch, // Search term for patient name or phone
    sortBy, 
    sortOrder 
  } = req.query;

  let baseQuery = `
    SELECT 
      a.id, a.start_time, a.end_time, a.status, a.notes, a.source, a.doctor_id,
      p.id as patient_pk, p.name as patient_name, p.phone as patient_phone
    FROM appointments a
    LEFT JOIN patients p ON a.patient_id = p.id AND p.hospital_id = a.hospital_id
  `; 

  let countQuery = `
    SELECT COUNT(DISTINCT a.id) as total_items
    FROM appointments a
    LEFT JOIN patients p ON a.patient_id = p.id AND p.hospital_id = a.hospital_id
  `;

  const conditions = ["a.hospital_id = $1"];
  const queryParams = [hospitalId];
  let paramIndex = 2;

  if (startDate) {
    conditions.push(`DATE(a.start_time) >= $${paramIndex++}`);
    queryParams.push(startDate);
  }
  if (endDate) {
    // To include the whole end day, we can use < next day or adjust time component if start_time is timestamp
    conditions.push(`DATE(a.start_time) <= $${paramIndex++}`); 
    queryParams.push(endDate);
  }
  if (status) {
    conditions.push(`a.status = $${paramIndex++}`);
    queryParams.push(status);
  }
  if (doctorId) {
    conditions.push(`a.doctor_id = $${paramIndex++}`);
    queryParams.push(doctorId);
  }
  if (patientId) {
    conditions.push(`a.patient_id = $${paramIndex++}`);
    queryParams.push(patientId);
  }
  
  // Add patient search by name or phone
  if (patientSearch) {
    conditions.push(`(p.name ILIKE $${paramIndex} OR p.phone ILIKE $${paramIndex})`);
    queryParams.push(`%${patientSearch}%`);
    paramIndex++;
  }

  if (conditions.length > 0) {
    const whereClause = " WHERE " + conditions.join(" AND ");
    baseQuery += whereClause;
    countQuery += whereClause;
  }

  // Sorting - carefully validate sortBy to prevent SQL injection if directly concatenating
  const validSortColumns = {
    'start_time': 'a.start_time',
    'patient_name': 'p.name',
    'status': 'a.status'
  };
  const sortColumn = validSortColumns[sortBy] || 'a.start_time'; // Default sort
  const order = (sortOrder && sortOrder.toUpperCase() === 'DESC') ? 'DESC' : 'ASC';
  baseQuery += ` ORDER BY ${sortColumn} ${order}`;

  baseQuery += ` LIMIT $${paramIndex++} OFFSET $${paramIndex++}`;
  queryParams.push(limit, offset);

  let pool;
  try {
    pool = await getHospitalDbPool(hospitalId);
  } catch (poolError) {
    logger.error(`[API_APPOINTMENTS] Error getting pool for hospital ${hospitalId}: ${poolError.message}`);
    return res.status(503).json({ success: false, message: `Database service unavailable: ${poolError.message}` });
  }

  try {
    const client = await pool.connect();
    try {
      logger.debug(`[API_APPOINTMENTS] Executing query: ${baseQuery} with params: ${JSON.stringify(queryParams)}`);
      const appointmentsResult = await client.query(baseQuery, queryParams);
      
      // For countQuery, we use the same params *except* limit and offset
      const countQueryParams = queryParams.slice(0, -2); 
      logger.debug(`[API_APPOINTMENTS] Executing count query: ${countQuery} with params: ${JSON.stringify(countQueryParams)}`);
      const totalResult = await client.query(countQuery, countQueryParams);
      const totalItems = parseInt(totalResult.rows[0].total_items, 10);
      
      return res.status(200).json({
        success: true,
        data: {
          appointments: appointmentsResult.rows,
          pagination: {
            totalItems,
            totalPages: Math.ceil(totalItems / limit),
            currentPage: page,
            pageSize: limit,
          },
        },
      });
    } finally {
      client.release();
    }
  } catch (error) {
    logger.error(`[API_APPOINTMENTS] Error fetching appointments for hospital ${hospitalId}:`, error);
    return res.status(500).json({ success: false, message: `Internal server error: ${error.message}` });
  }
});
