# Shared LLM Service

This directory contains the unified LLM service for the Voice Health Portal project. It replaces the previous fine-tuning approach with a more flexible function calling system using GPT-4.1 nano.

## Overview

The shared LLM service provides:

- **Unified LLM Interface**: Single service used by both voice_agent and whatsapp_agent
- **Function Calling**: Instead of fine-tuning, uses OpenAI's function calling for hospital operations
- **Async Support**: Built with AsyncOpenAI for better performance under high concurrency
- **Multilingual Support**: Supports Hindi (primary), Bengali, and English
- **Redis Caching**: Reduces costs and latency through intelligent caching
- **Production Ready**: Proper error handling, logging, and monitoring

## Architecture

```
shared/
├── __init__.py                 # Package initialization
├── llm_service.py             # Core LLM service with function calling
├── function_handlers.py       # Function implementations for LLM calls
├── llm_api_server.py         # HTTP API server for external access
├── config.py                 # Configuration management
├── start_llm_server.py       # Server startup script
├── requirements.txt          # Python dependencies
└── README.md                 # This file
```

## Key Components

### LLM Service (`llm_service.py`)
- Core service using AsyncOpenAI client
- Function registration and calling system
- Usage tracking and cost optimization
- Multilingual prompt generation

### Function Handlers (`function_handlers.py`)
- Implementations of functions that LLM can call
- Hospital data retrieval (doctors, tests, schedules)
- Booking operations (appointments, tests)
- Integration with Firebase and PostgreSQL

### API Server (`llm_api_server.py`)
- FastAPI-based HTTP server
- RESTful endpoints for external access
- Used by WhatsApp agent (Node.js) to access Python LLM service
- Health checks and monitoring endpoints

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set Environment Variables**:
   ```bash
   export OPENAI_API_KEY="your-openai-api-key"
   export REDIS_URL="redis://localhost:6379"  # Optional
   ```

3. **Start the API Server**:
   ```bash
   python start_llm_server.py
   ```

## Usage

### From Voice Agent (Python)
```python
from shared.llm_integration import process_voice_input

context = {
    "hospital_id": "hospital_123",
    "language": "hi",
    "hospital_name": "City Hospital",
    "user_info": {"phone": "+91XXXXXXXXXX"}
}

result = await process_voice_input("I want to book an appointment", context)
```

### From WhatsApp Agent (Node.js)
```javascript
import { whatsappLLMIntegration } from './lib/llm_integration.js';

const result = await whatsappLLMIntegration.processMessage(message, hospitalContext);
```

### Direct HTTP API
```bash
curl -X POST http://localhost:8001/process \
  -H "Content-Type: application/json" \
  -d '{
    "message": "I want to book an appointment",
    "context": {
      "hospital_id": "hospital_123",
      "language": "hi"
    }
  }'
```

## Available Functions

The LLM can call these functions to perform hospital operations:

- `get_hospital_info(hospital_id)` - Get hospital information
- `get_available_doctors(hospital_id, date)` - Get available doctors
- `get_doctor_schedule(hospital_id, doctor_id, date)` - Get doctor schedule
- `get_available_tests(hospital_id, date)` - Get available tests
- `book_appointment(hospital_id, patient_name, phone, doctor_id, date, time)` - Book appointment
- `book_test(hospital_id, patient_name, phone, test_id, date, time)` - Book test

## Configuration

Configuration is managed through environment variables and the `config.py` file:

- **LLM Settings**: Model, temperature, max tokens
- **API Settings**: OpenAI API key, timeout, retries
- **Cache Settings**: Redis configuration, TTL
- **Language Settings**: Supported languages, default language

## Monitoring

### Health Check
```bash
curl http://localhost:8001/health
```

### Usage Statistics
```bash
curl http://localhost:8001/usage
```

### Available Functions
```bash
curl http://localhost:8001/functions
```

## Migration from Old System

The new system replaces:

- `voice_agent/llm_cost_optimizer.py` → `shared/llm_service.py`
- `voice_agent/llm_fine_tuning.py` → Function calling approach
- Fine-tuned models → GPT-4.1 nano with function calling

### Benefits of New Approach

1. **No Fine-tuning Required**: Faster deployment, easier updates
2. **Real-time Data**: Functions access live hospital data
3. **Better Accuracy**: GPT-4.1 nano is more capable than fine-tuned models
4. **Unified Service**: Single service for both applications
5. **Cost Effective**: Function calling is more cost-effective than fine-tuning

## Development

### Adding New Functions

1. Define function in `llm_service.py`:
```python
self.register_function(
    name="new_function",
    description="Description for LLM",
    parameters={...}  # JSON schema
)
```

2. Implement handler in `function_handlers.py`:
```python
async def new_function(self, param1: str) -> Dict[str, Any]:
    # Implementation
    return {"success": True, "result": "..."}
```

3. Register handler:
```python
def get_function_handlers():
    return {
        "new_function": hospital_handlers.new_function,
        # ... other handlers
    }
```

### Testing

The service includes comprehensive error handling and fallback mechanisms:

- LLM service failures fall back to cached responses
- Function call failures return error messages
- Network issues are handled gracefully

## Production Deployment

1. **Environment Variables**: Set all required environment variables
2. **Redis**: Configure Redis for caching (optional but recommended)
3. **Monitoring**: Use health check endpoint for monitoring
4. **Scaling**: Service is stateless and can be horizontally scaled
5. **Security**: Configure CORS and authentication as needed

## Troubleshooting

### Common Issues

1. **OpenAI API Key**: Ensure OPENAI_API_KEY is set correctly
2. **Redis Connection**: Check Redis URL and connectivity
3. **Function Handlers**: Ensure voice_agent functions are properly imported
4. **Port Conflicts**: Default port is 8001, change if needed

### Logs

Logs are written to both console and `llm_service.log` file. Check logs for detailed error information.
