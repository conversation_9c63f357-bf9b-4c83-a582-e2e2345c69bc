/**
 * Redis Client for Staff Portal
 * Handles communication with voice agent <PERSON><PERSON> cache for booking limit management
 */

import Redis from 'ioredis';
import { logger } from './logger';

class RedisClient {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxRetries = 3;
  }

  /**
   * Initialize Redis connection
   */
  async connect() {
    if (this.isConnected && this.client) {
      return this.client;
    }

    try {
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379/0';
      
      this.client = new Redis(redisUrl, {
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        keepAlive: 30000,
        connectTimeout: 10000,
        commandTimeout: 5000,
      });

      // Test connection
      await this.client.ping();
      this.isConnected = true;
      this.connectionAttempts = 0;
      
      logger.info('[REDIS_CLIENT] Connected to Redis successfully');
      
      // Handle connection events
      this.client.on('error', (error) => {
        logger.error('[REDIS_CLIENT] Redis connection error:', error);
        this.isConnected = false;
      });

      this.client.on('close', () => {
        logger.warn('[REDIS_CLIENT] Redis connection closed');
        this.isConnected = false;
      });

      this.client.on('reconnecting', () => {
        logger.info('[REDIS_CLIENT] Redis reconnecting...');
      });

      return this.client;
    } catch (error) {
      this.connectionAttempts++;
      logger.error(`[REDIS_CLIENT] Failed to connect to Redis (attempt ${this.connectionAttempts}):`, error);
      
      if (this.connectionAttempts >= this.maxRetries) {
        logger.error('[REDIS_CLIENT] Max Redis connection attempts reached');
        throw new Error('Redis connection failed after maximum retries');
      }
      
      throw error;
    }
  }

  /**
   * Get Redis client instance
   */
  async getClient() {
    if (!this.isConnected || !this.client) {
      await this.connect();
    }
    return this.client;
  }

  /**
   * Check if Redis is connected
   */
  async isRedisConnected() {
    try {
      if (!this.client) return false;
      await this.client.ping();
      return true;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Redis ping failed:', error);
      return false;
    }
  }

  /**
   * Production-safe key scanning using SCAN instead of KEYS
   * @param {string} pattern - Redis key pattern to match
   * @param {number} count - Number of keys to return per iteration (default: 100)
   * @returns {Promise<string[]>} Array of matching keys
   */
  async scanKeys(pattern, count = 100) {
    try {
      const client = await this.getClient();
      const keys = [];
      const stream = client.scanStream({
        match: pattern,
        count: count
      });

      await new Promise((resolve, reject) => {
        stream.on('data', (resultKeys) => {
          keys.push(...resultKeys);
        });
        stream.on('end', resolve);
        stream.on('error', reject);
      });

      return keys;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error scanning keys with pattern:', pattern, error);
      return [];
    }
  }

  /**
   * Get doctor's daily booking limits from Redis
   */
  async getDoctorDailyLimits(hospitalId, doctorId) {
    try {
      const client = await this.getClient();
      const key = `booking_limits:${hospitalId}:${doctorId}:daily_limits`;
      const data = await client.get(key);
      
      if (data) {
        return JSON.parse(data);
      }
      return null;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error getting doctor daily limits:', error);
      return null;
    }
  }

  /**
   * Get current booking counter for a doctor on a specific date
   */
  async getBookingCounter(hospitalId, doctorId, date) {
    try {
      const client = await this.getClient();
      const key = `booking_counter:${hospitalId}:${doctorId}:${date}`;
      const count = await client.get(key);
      return count ? parseInt(count) : 0;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error getting booking counter:', error);
      return 0;
    }
  }

  /**
   * Get next available date for a doctor
   */
  async getNextAvailableDate(hospitalId, doctorId) {
    try {
      const client = await this.getClient();
      const key = `booking_limits:${hospitalId}:${doctorId}:next_available`;
      return await client.get(key);
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error getting next available date:', error);
      return null;
    }
  }

  /**
   * Trigger manual refresh of booking limits in voice agent
   */
  async triggerBookingLimitRefresh(hospitalId = null) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const voiceAgentUrl = process.env.VOICE_AGENT_URL || 'http://localhost:8000';
      const refreshUrl = `${voiceAgentUrl}/api/booking-limits/refresh`;

      const response = await fetch(refreshUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          hospital_id: hospitalId
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Voice agent refresh failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      logger.info('[REDIS_CLIENT] Voice agent booking limit refresh triggered:', result);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        logger.error('[REDIS_CLIENT] Voice agent refresh request timed out after 10 seconds');
        return {
          success: false,
          error: 'Request timed out after 10 seconds'
        };
      }

      logger.error('[REDIS_CLIENT] Error triggering voice agent refresh:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get booking availability status for a doctor
   */
  async getBookingAvailabilityStatus(hospitalId, doctorId, date = null) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    try {
      if (!date) {
        date = new Date().toISOString().split('T')[0]; // Today's date in YYYY-MM-DD format
      }

      const voiceAgentUrl = process.env.VOICE_AGENT_URL || 'http://localhost:8000';
      const statusUrl = `${voiceAgentUrl}/api/booking-limits/status/${hospitalId}/${doctorId}?date=${date}`;

      const response = await fetch(statusUrl, {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Status check failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      return {
        success: true,
        data: result
      };
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        logger.error('[REDIS_CLIENT] Booking availability status request timed out after 5 seconds');
        return {
          success: false,
          error: 'Request timed out after 5 seconds'
        };
      }

      logger.error('[REDIS_CLIENT] Error getting booking availability status:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get availability status for a doctor or test on a specific date
   */
  async getAvailabilityStatus(hospitalId, itemId, itemType, date) {
    try {
      const client = await this.getClient();
      const key = `availability:${hospitalId}:${itemType}:${itemId}:${date}`;
      const status = await client.get(key);
      return status !== null ? JSON.parse(status) : null;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error getting availability status:', error);
      return null;
    }
  }

  /**
   * Set availability status for a doctor or test on a specific date
   */
  async setAvailabilityStatus(hospitalId, itemId, itemType, date, isAvailable) {
    try {
      const client = await this.getClient();
      const key = `availability:${hospitalId}:${itemType}:${itemId}:${date}`;
      await client.setex(key, 86400, JSON.stringify(isAvailable)); // 24 hour TTL
      return true;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error setting availability status:', error);
      return false;
    }
  }

  /**
   * Get all available doctors for a hospital on a specific date
   */
  async getAvailableDoctors(hospitalId, date) {
    try {
      const client = await this.getClient();
      const pattern = `availability:${hospitalId}:doctor:*:${date}`;

      // Use production-safe SCAN instead of KEYS
      const keys = await this.scanKeys(pattern);

      const availableDoctors = [];
      for (const key of keys) {
        const isAvailable = await client.get(key);
        if (isAvailable && JSON.parse(isAvailable) === true) {
          // Extract doctor ID from key
          const doctorId = key.split(':')[3];
          availableDoctors.push(doctorId);
        }
      }

      return availableDoctors;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error getting available doctors:', error);
      return [];
    }
  }

  /**
   * Get all available tests for a hospital on a specific date
   */
  async getAvailableTests(hospitalId, date) {
    try {
      const client = await this.getClient();
      const pattern = `availability:${hospitalId}:test:*:${date}`;

      // Use production-safe SCAN instead of KEYS
      const keys = await this.scanKeys(pattern);

      const availableTests = [];
      for (const key of keys) {
        const isAvailable = await client.get(key);
        if (isAvailable && JSON.parse(isAvailable) === true) {
          // Extract test ID from key
          const testId = key.split(':')[3];
          availableTests.push(testId);
        }
      }

      return availableTests;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error getting available tests:', error);
      return [];
    }
  }

  /**
   * Trigger manual refresh of availability data in voice agent
   */
  async triggerAvailabilityRefresh(hospitalId, date = null) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const voiceAgentUrl = process.env.VOICE_AGENT_URL || 'http://localhost:8000';
      const refreshUrl = `${voiceAgentUrl}/api/availability/refresh`;

      const response = await fetch(refreshUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          hospital_id: hospitalId,
          date: date
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Voice agent availability refresh failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      logger.info('[REDIS_CLIENT] Voice agent availability refresh triggered:', result);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        logger.error('[REDIS_CLIENT] Voice agent availability refresh request timed out after 10 seconds');
        return {
          success: false,
          error: 'Request timed out after 10 seconds'
        };
      }

      logger.error('[REDIS_CLIENT] Error triggering voice agent availability refresh:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Close Redis connection
   */
  async disconnect() {
    if (this.client) {
      try {
        await this.client.quit();
        logger.info('[REDIS_CLIENT] Redis connection closed gracefully');
      } catch (error) {
        logger.error('[REDIS_CLIENT] Error closing Redis connection:', error);
      } finally {
        this.client = null;
        this.isConnected = false;
      }
    }
  }
}

// Create singleton instance
const redisClient = new RedisClient();

export default redisClient;
