# Voice Agent Environment Configuration Example
# Copy this file to .env and update with your actual values

# Server Configuration
PORT=8000
HOST=0.0.0.0
DEBUG=True
LOG_LEVEL=INFO

# Database Configuration
POSTGRES_USER=hospital_user
POSTGRES_PASSWORD=hospitaldb
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=hospital_db

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
GPT_MODEL=gpt-4o-mini

# Semantic Model Configuration (NEW)
# Use multilingual model for better Hindi/Bengali support (default)
SEMANTIC_MODEL=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2

# Alternative: Use English-only model for testing/fallback
# SEMANTIC_MODEL=all-MiniLM-L6-v2

# Alternative: Use custom model
# SEMANTIC_MODEL=your-custom-model-name

# Jambonz Configuration (Production)
JAMBONZ_API_KEY=your_jambonz_api_key
JAMBONZ_ACCOUNT_SID=your_jambonz_account_sid
JAMBONZ_API_URL=https://api.jambonz.cloud/v1

# Twilio Configuration (Testing)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Firebase Configuration
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/firebase-credentials.json
