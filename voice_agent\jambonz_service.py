import json
import os
import logging
import requests
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

from .telephony import dispatch_sms as telephony_dispatch_sms

# Module-local logger – configuration stays with the host app
logger = logging.getLogger(__name__)

class JambonzService:
    """
    Service for interacting with Jambonz API.
    Handles phone calls, webhooks, and SIP trunking for the voice agent.
    """
    
    def __init__(self):
        """Initialize Jambonz service with API credentials."""
        self.api_key = os.environ.get("JAMBONZ_API_KEY")
        self.account_sid = os.environ.get("JAMBONZ_ACCOUNT_SID")
        self.base_url = os.environ.get("JAMBONZ_API_URL", "https://api.jambonz.cloud/v1")
        
        if not self.api_key or not self.account_sid:
            logger.warning("Jambonz credentials not found in environment variables")
            
        # Optional environment variables
        self.region = os.environ.get("JAMBONZ_REGION", "us")
        
        # Headers for API requests
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        } if self.api_key else {"Content-Type": "application/json"}
    
    def validate_credentials(self) -> Dict[str, Any]:
        """
        Validate Jambonz API credentials.
        
        Returns:
            Dict with validation status and details
        """
        if not self.api_key or not self.account_sid:
            return {
                "valid": False,
                "message": "Missing Jambonz API credentials in environment variables"
            }
            
        try:
            # Try to get account info to validate credentials
            url = f"{self.base_url}/Accounts/{self.account_sid}"
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                return {
                    "valid": True,
                    "account_info": response.json()
                }
            else:
                return {
                    "valid": False,
                    "status_code": response.status_code,
                    "message": response.text
                }
        except Exception as e:
            logger.error(f"Error validating Jambonz credentials: {str(e)}")
            return {
                "valid": False,
                "message": f"Error validating credentials: {str(e)}"
            }
    
    def make_outbound_call(self,
                          from_number: str,
                          to_number: str,
                          webhook_url: str,
                          call_hook: str = "dial",
                          timeout: int = 30,
                          extra_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Make an outbound call using Jambonz API.
        
        Args:
            from_number: Caller ID number
            to_number: Destination phone number
            webhook_url: URL to receive call events
            call_hook: Call hook type ('dial', 'sip', 'teams', etc.)
            timeout: Call timeout in seconds
            extra_params: Additional parameters for the API call
            
        Returns:
            Dict with API response or error details
        """
        if not self.api_key or not self.account_sid:
            return {
                "success": False,
                "message": "Missing Jambonz API credentials"
            }
            
        try:
            url = f"{self.base_url}/Accounts/{self.account_sid}/Calls"
            
            # Basic payload for the call
            payload = {
                "call_hook": call_hook,
                "from": from_number,
                "to": to_number,
                "url": webhook_url,
                "timeout": timeout
            }
            
            # Add any extra parameters
            if extra_params:
                payload.update(extra_params)
                
            response = requests.post(url, headers=self.headers, json=payload)
            
            if response.status_code in (200, 201):
                return {
                    "success": True,
                    "call_details": response.json()
                }
            else:
                logger.error(f"Failed to make outbound call: {response.text}")
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "message": response.text
                }
        except Exception as e:
            logger.error(f"Error making outbound call: {str(e)}")
            return {
                "success": False,
                "message": f"Error making call: {str(e)}"
            }
    
    def terminate_call(self, call_sid: str) -> Dict[str, Any]:
        """
        Terminate an active call.
        
        Args:
            call_sid: SID of the call to terminate
            
        Returns:
            Dict with status information
        """
        if not self.api_key or not self.account_sid:
            return {
                "success": False,
                "message": "Missing Jambonz API credentials"
            }
            
        try:
            url = f"{self.base_url}/Accounts/{self.account_sid}/Calls/{call_sid}"
            response = requests.delete(url, headers=self.headers)
            
            if response.status_code in (200, 204):
                return {
                    "success": True,
                    "message": "Call terminated successfully"
                }
            else:
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "message": response.text
                }
        except Exception as e:
            logger.error(f"Error terminating call: {str(e)}")
            return {
                "success": False,
                "message": f"Error terminating call: {str(e)}"
            }
    
    def get_call_details(self, call_sid: str) -> Dict[str, Any]:
        """
        Get details of a specific call.
        
        Args:
            call_sid: SID of the call
            
        Returns:
            Dict with call details or error information
        """
        if not self.api_key or not self.account_sid:
            return {
                "success": False,
                "message": "Missing Jambonz API credentials"
            }
            
        try:
            url = f"{self.base_url}/Accounts/{self.account_sid}/Calls/{call_sid}"
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "call_details": response.json()
                }
            else:
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "message": response.text
                }
        except Exception as e:
            logger.error(f"Error getting call details: {str(e)}")
            return {
                "success": False,
                "message": f"Error getting call details: {str(e)}"
            }
    
    def list_active_calls(self) -> Dict[str, Any]:
        """
        List all active calls for the account.
        
        Returns:
            Dict with list of active calls or error information
        """
        if not self.api_key or not self.account_sid:
            return {
                "success": False,
                "message": "Missing Jambonz API credentials"
            }
            
        try:
            url = f"{self.base_url}/Accounts/{self.account_sid}/Calls"
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "calls": response.json()
                }
            else:
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "message": response.text
                }
        except Exception as e:
            logger.error(f"Error listing calls: {str(e)}")
            return {
                "success": False,
                "message": f"Error listing calls: {str(e)}"
            }
    
    def generate_call_response(self, actions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate Jambonz-compatible webhook response with call actions.
        
        Args:
            actions: List of action objects for Jambonz
            
        Returns:
            Dict with Jambonz webhook response format
        """
        return {"verb": "gather", "actions": actions}
    
    def create_say_action(self, text: str, language: str = "en-US", voice: str = None) -> Dict[str, Any]:
        """
        Create a 'say' action for text-to-speech.
        
        Args:
            text: Text to be spoken
            language: Language code
            voice: Optional voice name
            
        Returns:
            Dict with 'say' action configuration
        """
        action = {
            "verb": "say",
            "text": text,
            "language": language
        }
        
        if voice:
            action["voice"] = voice
            
        return action
    
    def create_gather_action(self, 
                           say: Dict[str, Any] = None,
                           play: Dict[str, Any] = None,
                           input_type: str = "dtmf speech",
                           timeout: int = 5,
                           action_hook: str = None,
                           finish_on_key: str = "#",
                           num_digits: int = None,
                           speech_timeout: int = 2,
                           language: str = "en-US") -> Dict[str, Any]:
        """
        Create a 'gather' action to collect user input.
        
        Args:
            say: Optional 'say' action to include
            play: Optional 'play' action to include
            input_type: Input types to gather ('dtmf', 'speech', or 'dtmf speech')
            timeout: Timeout in seconds
            action_hook: Webhook URL for the gather result
            finish_on_key: Key to end DTMF input
            num_digits: Number of DTMF digits to collect
            speech_timeout: Speech timeout in seconds
            language: Language for speech recognition
            
        Returns:
            Dict with 'gather' action configuration
        """
        action = {
            "verb": "gather",
            "input": input_type,
            "timeout": timeout,
            "actionHook": action_hook,
            "finishOnKey": finish_on_key,
            "language": language
        }
        
        if speech_timeout:
            action["speechTimeout"] = speech_timeout
            
        if num_digits:
            action["numDigits"] = num_digits
            
        if say:
            action["say"] = say
            
        if play:
            action["play"] = play
            
        return action
    
    def create_play_action(self, url: str, loop: int = 1) -> Dict[str, Any]:
        """
        Create a 'play' action to play audio.
        
        Args:
            url: URL of the audio file
            loop: Number of times to loop the audio
            
        Returns:
            Dict with 'play' action configuration
        """
        return {
            "verb": "play",
            "url": url,
            "loop": loop
        }
    
    def create_record_action(self, 
                           action_hook: str,
                           beep: bool = True,
                           finish_on_key: str = "#",
                           max_length: int = 60,
                           silence_threshold: int = 500,
                           silence_timeout: int = 5) -> Dict[str, Any]:
        """
        Create a 'record' action to record audio.
        
        Args:
            action_hook: Webhook URL for the recording
            beep: Whether to play a beep before recording
            finish_on_key: Key to end recording
            max_length: Maximum recording length in seconds
            silence_threshold: Silence threshold for auto-termination
            silence_timeout: Silence timeout in seconds
            
        Returns:
            Dict with 'record' action configuration
        """
        return {
            "verb": "record",
            "actionHook": action_hook,
            "beep": beep,
            "finishOnKey": finish_on_key,
            "maxLength": max_length,
            "silenceThreshold": silence_threshold,
            "silenceTimeout": silence_timeout
        }
    
    def create_dial_action(self, 
                          target: str,
                          from_number: str = None,
                          timeout: int = 30,
                          action_hook: str = None) -> Dict[str, Any]:
        """
        Create a 'dial' action to make a call.
        
        Args:
            target: Destination phone number
            from_number: Caller ID number
            timeout: Call timeout in seconds
            action_hook: Webhook URL for dial status
            
        Returns:
            Dict with 'dial' action configuration
        """
        action = {
            "verb": "dial",
            "target": target,
            "timeout": timeout
        }
        
        if from_number:
            action["from"] = from_number
            
        if action_hook:
            action["actionHook"] = action_hook
            
        return action
    
    def create_conference_action(self, 
                               name: str,
                               start_conference_on_enter: bool = True,
                               end_conference_on_exit: bool = False) -> Dict[str, Any]:
        """
        Create a 'conference' action for conference calls.
        
        Args:
            name: Conference name
            start_conference_on_enter: Whether to start conference when this user enters
            end_conference_on_exit: Whether to end conference when this user exits
            
        Returns:
            Dict with 'conference' action configuration
        """
        return {
            "verb": "conference",
            "name": name,
            "startConferenceOnEnter": start_conference_on_enter,
            "endConferenceOnExit": end_conference_on_exit
        }
    
    def create_sip_trunk(self, 
                        name: str, 
                        service_provider: str,
                        register_username: str = None,
                        register_password: str = None,
                        register_sip_realm: str = None,
                        register_from_user: str = None,
                        register_from_domain: str = None,
                        register_public_ip_in_contact: bool = False) -> Dict[str, Any]:
        """
        Create a SIP trunk for a hospital.
        
        Args:
            name: Trunk name
            service_provider: SIP service provider
            register_username: SIP registration username
            register_password: SIP registration password
            register_sip_realm: SIP realm
            register_from_user: Username for From header
            register_from_domain: Domain for From header
            register_public_ip_in_contact: Whether to use public IP in Contact header
            
        Returns:
            Dict with API response or error details
        """
        if not self.api_key or not self.account_sid:
            return {
                "success": False,
                "message": "Missing Jambonz API credentials"
            }
            
        try:
            url = f"{self.base_url}/Accounts/{self.account_sid}/VoipCarriers"
            
            payload = {
                "name": name,
                "service_provider": service_provider,
                "e164_leading_plus": True,
                "requires_register": True if register_username and register_password else False
            }
            
            # Add registration details if provided
            if register_username and register_password:
                payload.update({
                    "register_username": register_username,
                    "register_password": register_password,
                    "register_sip_realm": register_sip_realm,
                    "register_from_user": register_from_user,
                    "register_from_domain": register_from_domain,
                    "register_public_ip_in_contact": register_public_ip_in_contact
                })
                
            response = requests.post(url, headers=self.headers, json=payload)
            
            if response.status_code in (200, 201):
                return {
                    "success": True,
                    "trunk_details": response.json()
                }
            else:
                logger.error(f"Failed to create SIP trunk: {response.text}")
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "message": response.text
                }
        except Exception as e:
            logger.error(f"Error creating SIP trunk: {str(e)}")
            return {
                "success": False,
                "message": f"Error creating SIP trunk: {str(e)}"
            }
    
    def list_sip_trunks(self) -> Dict[str, Any]:
        """
        List all SIP trunks for the account.
        
        Returns:
            Dict with list of SIP trunks or error information
        """
        if not self.api_key or not self.account_sid:
            return {
                "success": False,
                "message": "Missing Jambonz API credentials"
            }
            
        try:
            url = f"{self.base_url}/Accounts/{self.account_sid}/VoipCarriers"
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "trunks": response.json()
                }
            else:
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "message": response.text
                }
        except Exception as e:
            logger.error(f"Error listing SIP trunks: {str(e)}")
            return {
                "success": False,
                "message": f"Error listing SIP trunks: {str(e)}"
            }
    
    def get_sip_trunk(self, trunk_sid: str) -> Dict[str, Any]:
        """
        Get details of a specific SIP trunk.
        
        Args:
            trunk_sid: SID of the SIP trunk
            
        Returns:
            Dict with trunk details or error information
        """
        if not self.api_key or not self.account_sid:
            return {
                "success": False,
                "message": "Missing Jambonz API credentials"
            }
            
        try:
            url = f"{self.base_url}/Accounts/{self.account_sid}/VoipCarriers/{trunk_sid}"
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "trunk_details": response.json()
                }
            else:
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "message": response.text
                }
        except Exception as e:
            logger.error(f"Error getting SIP trunk details: {str(e)}")
            return {
                "success": False,
                "message": f"Error getting SIP trunk details: {str(e)}"
            }
    
    def update_sip_trunk(self, trunk_sid: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a SIP trunk.
        
        Args:
            trunk_sid: SID of the SIP trunk to update
            updates: Dict with fields to update
            
        Returns:
            Dict with API response or error details
        """
        if not self.api_key or not self.account_sid:
            return {
                "success": False,
                "message": "Missing Jambonz API credentials"
            }
            
        try:
            url = f"{self.base_url}/Accounts/{self.account_sid}/VoipCarriers/{trunk_sid}"
            response = requests.put(url, headers=self.headers, json=updates)
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "trunk_details": response.json()
                }
            else:
                logger.error(f"Failed to update SIP trunk: {response.text}")
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "message": response.text
                }
        except Exception as e:
            logger.error(f"Error updating SIP trunk: {str(e)}")
            return {
                "success": False,
                "message": f"Error updating SIP trunk: {str(e)}"
            }
    
    def delete_sip_trunk(self, trunk_sid: str) -> Dict[str, Any]:
        """
        Delete a SIP trunk.
        
        Args:
            trunk_sid: SID of the SIP trunk to delete
            
        Returns:
            Dict with status information
        """
        if not self.api_key or not self.account_sid:
            return {
                "success": False,
                "message": "Missing Jambonz API credentials"
            }
            
        try:
            url = f"{self.base_url}/Accounts/{self.account_sid}/VoipCarriers/{trunk_sid}"
            response = requests.delete(url, headers=self.headers)
            
            if response.status_code in (200, 204):
                return {
                    "success": True,
                    "message": "SIP trunk deleted successfully"
                }
            else:
                return {
                    "success": False,
                    "status_code": response.status_code,
                    "message": response.text
                }
        except Exception as e:
            logger.error(f"Error deleting SIP trunk: {str(e)}")
            return {
                "success": False,
                "message": f"Error deleting SIP trunk: {str(e)}"
            }

    def send_sms(self, to_number: str, from_number: str, message: str, hospital_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Send SMS via carrier-specific mechanism.
        Direct SMS sending via Jambonz generic API is not currently supported.
        """
        logger.info(f"Attempting to send SMS via JambonzService to {to_number} from {from_number}")
        logger.warning("Direct SMS sending via a generic Jambonz API is not available based on current documentation.")
        logger.info("Attempting to dispatch SMS via carrier-specific mechanism (telephony.dispatch_sms).")

        try:
            # This function is expected to be in telephony.py and handle actual sending
            result = telephony_dispatch_sms(
                to_number=to_number,
                from_number=from_number,
                message=message,
                hospital_id=hospital_id
            )
            # Assuming telephony_dispatch_sms returns a dict like {"success": True/False, "details": "..."}
            if result.get("success"):
                logger.info(f"SMS dispatched successfully via telephony module for hospital_id {hospital_id}.")
                return {"success": True, "message": "SMS dispatch attempted.", "details": result.get("details")}
            else:
                logger.error(f"SMS dispatch failed via telephony module for hospital_id {hospital_id}: {result.get('details')}")
                return {"success": False, "message": "SMS dispatch failed via telephony module.", "details": result.get("details")}
        except Exception as e:
            logger.error(f"Error calling telephony_dispatch_sms for hospital_id {hospital_id}: {str(e)}")
            # Simulate failure as telephony_dispatch_sms is a placeholder
            return {"success": False, "message": f"Error during SMS dispatch attempt: {str(e)}"}

# Create global instance
jambonz_service = JambonzService()