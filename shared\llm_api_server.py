"""
LLM API Server

This module provides an HTTP API server that exposes the unified LLM service
for use by the WhatsApp agent (Node.js) and other external services.
"""

import asyncio
import logging
from typing import Dict, Any
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from .llm_service import llm_service
from .function_handlers import hospital_handlers, get_function_handlers
from .config import get_config, validate_config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="Voice Health Portal LLM API",
    description="Unified LLM service API for Voice Health Portal",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class ProcessMessageRequest(BaseModel):
    message: str
    context: Dict[str, Any]

class ProcessMessageResponse(BaseModel):
    success: bool
    response: str
    function_calls: list = []
    function_results: Dict[str, Any] = {}
    error: str = None

class HealthCheckResponse(BaseModel):
    status: str
    version: str
    config_valid: bool
    issues: list = []

class UsageStatsResponse(BaseModel):
    total_calls: int
    cached_calls: int
    total_tokens: int
    estimated_cost: str
    cache_hit_rate: str

# API endpoints
@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Health check endpoint."""
    config_status = validate_config()
    return HealthCheckResponse(
        status="healthy" if config_status["valid"] else "degraded",
        version="1.0.0",
        config_valid=config_status["valid"],
        issues=config_status["issues"]
    )

@app.post("/process", response_model=ProcessMessageResponse)
async def process_message(request: ProcessMessageRequest):
    """Process a message using the LLM service."""
    try:
        result = await llm_service.process_message(request.message, request.context)
        
        return ProcessMessageResponse(
            success=result.get("success", False),
            response=result.get("response", ""),
            function_calls=result.get("function_calls", []),
            function_results=result.get("function_results", {}),
            error=result.get("error")
        )
        
    except Exception as e:
        logger.error(f"Error processing message: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/usage", response_model=UsageStatsResponse)
async def get_usage_stats():
    """Get LLM usage statistics."""
    try:
        stats = llm_service.get_usage_stats()
        return UsageStatsResponse(**stats)
    except Exception as e:
        logger.error(f"Error getting usage stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/usage/reset")
async def reset_usage_stats():
    """Reset LLM usage statistics."""
    try:
        llm_service.reset_usage_stats()
        return {"message": "Usage statistics reset successfully"}
    except Exception as e:
        logger.error(f"Error resetting usage stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/config")
async def get_configuration():
    """Get current configuration."""
    try:
        config = get_config()
        # Remove sensitive information
        if "api" in config and "openai_api_key" in config["api"]:
            config["api"]["openai_api_key"] = "***" if config["api"]["openai_api_key"] else None
        return config
    except Exception as e:
        logger.error(f"Error getting configuration: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/functions")
async def list_functions():
    """List available functions."""
    try:
        functions = list(llm_service.functions.keys())
        return {"functions": functions}
    except Exception as e:
        logger.error(f"Error listing functions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize the LLM service on startup."""
    logger.info("Starting LLM API server...")
    
    # Validate configuration
    config_status = validate_config()
    if not config_status["valid"]:
        logger.warning(f"Configuration issues: {config_status['issues']}")
    
    # Set up function handlers
    try:
        handlers = get_function_handlers()
        llm_service.set_function_handlers(handlers)
        logger.info(f"Registered {len(handlers)} function handlers")
    except Exception as e:
        logger.error(f"Error setting up function handlers: {e}")
    
    logger.info("LLM API server started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    logger.info("Shutting down LLM API server...")

# Main function to run the server
def run_server(host: str = "0.0.0.0", port: int = 8001, reload: bool = False):
    """Run the LLM API server."""
    uvicorn.run(
        "shared.llm_api_server:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )

if __name__ == "__main__":
    run_server()
