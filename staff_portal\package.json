{"name": "staff-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 5000", "build": "next build", "start": "next start -p 5000", "lint": "next lint", "db:init": "node scripts/init-db.js"}, "dependencies": {"@heroicons/react": "^2.2.0", "autoprefixer": "^10.4.14", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "cookie": "^0.6.0", "date-fns": "^2.30.0", "dotenv": "^16.5.0", "firebase": "^10.5.2", "firebase-admin": "^11.11.1", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "next": "13.5.6", "pg": "^8.11.3", "postcss": "^8.4.14", "react": "18.2.0", "react-dom": "18.2.0", "react-feather": "^2.0.10", "ssh2": "^1.14.0", "tailwindcss": "^3.3.0"}}