import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { Search, LogOut, Bell, Calendar, User, Settings, RefreshCw, FileText, AlertCircle } from 'react-feather';
import NotificationCenter from '../components/notifications/NotificationCenter';
import PrescriptionViewer from '../components/notifications/PrescriptionViewer';
import Layout from '../components/Layout';

export default function Dashboard() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [doctors, setDoctors] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState(null);
  const [searchLoading, setSearchLoading] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState(null);
  const [showNotificationModal, setShowNotificationModal] = useState(false);
  const [notificationCount, setNotificationCount] = useState(0);
  const [todayAppointments, setTodayAppointments] = useState([]);

  // Get user status on load
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const res = await fetch('/api/auth/status');
        const data = await res.json();
        
        if (data.success) {
          setUser(data.user);
          fetchDoctors(data.user.hospital_id);
          fetchBookings(data.user.hospital_id);
        } else {
          // Redirect to login if not authenticated
          router.push('/login');
        }
      } catch (error) {
        console.error('Auth check error:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };
    
    checkAuth();
  }, [router]);

  // Handle notification selection
  const handleNotificationSelect = (notification) => {
    setSelectedNotification(notification);
    setShowNotificationModal(true);
  };

  // Handle notification modal close
  const handleNotificationModalClose = () => {
    setShowNotificationModal(false);
    // Wait for animation to complete before removing the notification from state
    setTimeout(() => setSelectedNotification(null), 300);
  };

  // Update notification count
  const updateNotificationCount = (count) => {
    setNotificationCount(count);
  };
  
  // Fetch doctors for this hospital
  const fetchDoctors = async (hospitalId) => {
    try {
      setLoading(true);
      const res = await fetch(`/api/doctors?hospital_id=${hospitalId}`);
      const data = await res.json();
      
      if (data.success) {
        setDoctors(data.data);
      } else {
        console.error('Failed to fetch doctors:', data.message);
      }
    } catch (error) {
      console.error('Fetch doctors error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch today's bookings for this hospital
  const fetchBookings = async (hospitalId) => {
    if (!hospitalId) return; 
    try {
      const res = await fetch(`/api/hospital-bookings?hospital_id=${hospitalId}`);
      const data = await res.json();
      
      if (data.success && data.data && data.data.todayAppointments) {
        setTodayAppointments(data.data.todayAppointments);
      } else {
        console.error('Failed to fetch bookings:', data.message || 'No data returned');
        setTodayAppointments([]); 
      }
    } catch (error) {
      console.error('Fetch bookings error:', error);
      setTodayAppointments([]); 
    }
  };

  // Redirect to search page
  const goToSearch = () => {
    router.push('/search');
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST'
      });
      router.push('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Send reminder to all patients for a doctor
  const sendReminders = async (doctorId) => {
    try {
      const res = await fetch('/api/reminders/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          doctorId,
          method: 'sms'
        })
      });
      
      const data = await res.json();
      
      if (data.success) {
        alert(`Reminders sent successfully to ${data.data.appointmentsAffected} patients`);
      } else {
        alert(`Failed to send reminders: ${data.message}`);
      }
    } catch (error) {
      console.error('Send reminders error:', error);
      alert('Failed to send reminders. Please try again.');
    }
  };

  // Record doctor lateness and notify patients
  const recordDoctorLateness = async (doctorId, lateMinutes) => {
    try {
      const res = await fetch('/api/reminders/doctor-late', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          doctorId,
          lateMinutes,
          hospitalId: user.hospital_id
        })
      });
      
      const data = await res.json();
      
      if (data.success) {
        alert(`Late notifications sent to ${data.data.appointmentsAffected} patients`);
      } else {
        alert(`Failed to send late notifications: ${data.message}`);
      }
    } catch (error) {
      console.error('Doctor late notification error:', error);
      alert('Failed to send late notifications. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-2xl font-semibold text-slate-700 flex items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#003253] mr-3"></div>
          Loading...
        </div>
      </div>
    );
  }

  return (
    <Layout title="Dashboard" user={user} notificationCount={notificationCount}>
      {/* Notification modals */}
      {showNotificationModal && (
        <NotificationCenter 
          onClose={handleNotificationModalClose} 
          onSelect={handleNotificationSelect} 
          onCountUpdate={updateNotificationCount}
          userId={user?.id}
          hospitalId={user?.hospital_id}
        />
      )}
      
      {selectedNotification && selectedNotification.type === 'prescription' && (
        <PrescriptionViewer 
          prescription={selectedNotification.data} 
          onClose={handleNotificationModalClose} 
        />
      )}
      

      
      {/* Dashboard content starts here */}

      
      {/* Quick actions */}
      <div className="mb-8 bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-slate-200">
          <h2 className="text-lg font-semibold text-slate-800">Quick Actions</h2>
        </div>
        
        <div className="px-6 py-4">
          <div className="flex flex-wrap gap-4">
          </div>
        </div>
      </div>
      
      {/* Doctor list */}
      <div className="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-slate-200 flex justify-between items-center">
          <h2 className="text-lg font-semibold text-slate-800">Doctors</h2>
          
          {user && user.role === 'admin' && (
            <Link href="/admin" className="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-[#003253] hover:bg-[#004b7a] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#003253]">
              <Settings className="h-4 w-4 mr-1" />
              Manage Doctors
            </Link>
          )}
        </div>
        
        {doctors.length === 0 ? (
          <div className="px-6 py-12 text-center">
            <p className="text-slate-500">No doctors found for this hospital.</p>
          </div>
        ) : (
          <ul className="divide-y divide-slate-200">
            {doctors.map(doctor => {
              const doctorAppointmentsToday = todayAppointments.filter(app => app.doctor_id === doctor.id);
              
              return (
                <li key={doctor.id} className="px-6 py-4">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                    <div className="flex items-center mb-2 md:mb-0">
                      <div className="h-10 w-10 rounded-full bg-[#e6f0f9] flex items-center justify-center text-[#003253] mr-3">
                        <User className="h-6 w-6" />
                      </div>
                      <div>
                        <h3 className="text-md font-medium text-slate-800">Dr. {doctor.name}</h3>
                        <p className="text-sm text-slate-500">{doctor.specialty}</p>
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-2">
                      <button
                        onClick={() => sendReminders(doctor.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-[#003253] hover:bg-[#004b7a] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#003253]"
                      >
                        <Bell className="h-4 w-4 mr-1" />
                        Send Reminders
                      </button>
                      
                      <div className="relative inline-block text-left">
                        <button
                          onClick={() => {
                            const lateMinutes = prompt('How many minutes late is the doctor?', '15');
                            if (lateMinutes && !isNaN(parseInt(lateMinutes))) {
                              recordDoctorLateness(doctor.id, parseInt(lateMinutes));
                            }
                          }}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-amber-700 bg-amber-100 hover:bg-amber-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
                        >
                          <Clock className="h-4 w-4 mr-1" />
                          Doctor is Late
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-4 bg-slate-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-slate-700 mb-2">Today's Appointments ({doctorAppointmentsToday.length})</h4>
                    
                    {doctorAppointmentsToday.length > 0 ? (
                      <Link href={`/appointments?doctorId=${doctor.id}`} className="text-sm inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-white bg-[#003253] hover:bg-[#004b7a] focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-[#003253]">
                        View all appointments →
                      </Link>
                    ) : (
                      <p className="text-sm text-slate-500">No appointments scheduled for today</p>
                    )}
                  </div>
                </li>
              );
            })}
          </ul>
        )}
      </div>
    </Layout>
  );
}

// Clock component for the doctor is late button
function Clock(props) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <polyline points="12 6 12 12 16 14" />
    </svg>
  );
}
