/**
 * Firebase integration for WhatsApp Agent
 * 
 * Handles Firebase initialization and provides functions for interacting with Firestore
 */

import admin from 'firebase-admin';
import { logger } from './logger.js';

// Firebase app instance
let firebaseApp = null;

/**
 * Initialize Firebase Admin SDK
 */
export const initializeFirebase = async () => {
  try {
    // Check if already initialized
    if (firebaseApp) {
      logger.info('Firebase already initialized');
      return firebaseApp;
    }

    // Initialize with service account if provided
    if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
      firebaseApp = admin.initializeApp({
        credential: admin.credential.applicationDefault()
      });
      logger.info('Firebase initialized with application default credentials');
    } else {
      throw new Error('GOOGLE_APPLICATION_CREDENTIALS environment variable not set');
    }

    return firebaseApp;
  } catch (error) {
    logger.error('Firebase initialization error:', error);
    throw error;
  }
};

/**
 * Get Firestore database instance
 */
export const getFirestore = () => {
  if (!firebaseApp) {
    throw new Error('Firebase not initialized. Call initializeFirebase() first.');
  }
  return admin.firestore();
};

/**
 * Get doctor information from Firestore
 * @param {string} hospitalId - Hospital ID
 * @param {string} doctorId - Doctor ID
 * @returns {Promise<Object>} - Doctor data
 */
export const getDoctor = async (hospitalId, doctorId) => {
  try {
    const db = getFirestore();
    const doctorRef = db.collection(`hospital_${hospitalId}_data`)
                      .doc('doctors')
                      .collection('doctors')
                      .doc(doctorId);
    
    const doctorDoc = await doctorRef.get();
    
    if (!doctorDoc.exists) {
      logger.warn(`Doctor ${doctorId} not found for hospital ${hospitalId}`);
      return null;
    }
    
    return { id: doctorDoc.id, ...doctorDoc.data() };
  } catch (error) {
    logger.error(`Error getting doctor ${doctorId}:`, error);
    throw error;
  }
};

/**
 * Get all doctors for a hospital
 * @param {string} hospitalId - Hospital ID
 * @returns {Promise<Array>} - Array of doctor data
 */
export const getDoctors = async (hospitalId) => {
  try {
    const db = getFirestore();
    const doctorsRef = db.collection(`hospital_${hospitalId}_data`)
                       .doc('doctors')
                       .collection('doctors');
    
    const snapshot = await doctorsRef.get();
    
    if (snapshot.empty) {
      logger.warn(`No doctors found for hospital ${hospitalId}`);
      return [];
    }
    
    const doctors = [];
    snapshot.forEach(doc => {
      doctors.push({ id: doc.id, ...doc.data() });
    });
    
    return doctors;
  } catch (error) {
    logger.error(`Error getting doctors for hospital ${hospitalId}:`, error);
    throw error;
  }
};

/**
 * Get test information from Firestore
 * @param {string} hospitalId - Hospital ID
 * @param {string} testId - Test ID
 * @returns {Promise<Object>} - Test data
 */
export const getTest = async (hospitalId, testId) => {
  try {
    const db = getFirestore();
    const testRef = db.collection(`hospital_${hospitalId}_data`)
                     .doc('tests')
                     .collection('tests')
                     .doc(testId);
    
    const testDoc = await testRef.get();
    
    if (!testDoc.exists) {
      logger.warn(`Test ${testId} not found for hospital ${hospitalId}`);
      return null;
    }
    
    return { id: testDoc.id, ...testDoc.data() };
  } catch (error) {
    logger.error(`Error getting test ${testId}:`, error);
    throw error;
  }
};

/**
 * Get all tests for a hospital
 * @param {string} hospitalId - Hospital ID
 * @returns {Promise<Array>} - Array of test data
 */
export const getTests = async (hospitalId) => {
  try {
    const db = getFirestore();
    const testsRef = db.collection(`hospital_${hospitalId}_data`)
                     .doc('tests')
                     .collection('tests');
    
    const snapshot = await testsRef.get();
    
    if (snapshot.empty) {
      logger.warn(`No tests found for hospital ${hospitalId}`);
      return [];
    }
    
    const tests = [];
    snapshot.forEach(doc => {
      tests.push({ id: doc.id, ...doc.data() });
    });
    
    return tests;
  } catch (error) {
    logger.error(`Error getting tests for hospital ${hospitalId}:`, error);
    throw error;
  }
};

/**
 * Check doctor booking count against limit
 * @param {string} hospitalId - Hospital ID
 * @param {string} doctorId - Doctor ID
 * @param {string} date - Date in YYYY-MM-DD format
 * @returns {Promise<Object>} - Booking status with count and limit
 */
export const checkDoctorBookingLimit = async (hospitalId, doctorId, date) => {
  try {
    // Get doctor data to check booking limit
    const doctor = await getDoctor(hospitalId, doctorId);
    
    if (!doctor) {
      throw new Error(`Doctor ${doctorId} not found`);
    }
    
    const bookingLimit = doctor.booking_limit || 10; // Default to 10 if not set
    
    // First check if we have the current booking count in Firebase
    let currentBookings = 0;
    
    if (doctor.current_bookings && doctor.current_bookings[date] !== undefined) {
      // Use the count from Firebase if available
      currentBookings = doctor.current_bookings[date];
      logger.info(`Using booking count from Firebase for doctor ${doctorId} on ${date}: ${currentBookings}`);
    } else {
      // If not in Firebase, query PostgreSQL for current booking count
      // This will be implemented in the database.js module
      const db = await import('./database.js');
      currentBookings = await db.getDoctorBookingCount(hospitalId, doctorId, date);
      
      // Update Firebase with the current count (do this asynchronously)
      updateBookingCountInFirebase(hospitalId, doctorId, date, currentBookings).catch(err => {
        logger.error(`Error updating booking count in Firebase: ${err.message}`);
      });
    }
    
    return {
      doctorId,
      date,
      currentBookings,
      bookingLimit,
      isAvailable: currentBookings < bookingLimit
    };
  } catch (error) {
    logger.error(`Error checking booking limit for doctor ${doctorId}:`, error);
    throw error;
  }
};

/**
 * Update booking count in Firebase
 * @param {string} hospitalId - Hospital ID
 * @param {string} doctorId - Doctor ID
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {number} count - Current booking count
 * @returns {Promise<void>}
 */
async function updateBookingCountInFirebase(hospitalId, doctorId, date, count) {
  try {
    // Get doctor data from Firebase
    const doctor = await getDoctor(hospitalId, doctorId);
    if (!doctor) {
      throw new Error(`Doctor ${doctorId} not found in Firebase`);
    }
    
    // Update the doctor's current_bookings field
    if (!doctor.current_bookings) {
      doctor.current_bookings = {};
    }
    
    doctor.current_bookings[date] = count;
    
    // Update doctor in Firebase
    const db = getFirestore();
    const doctorRef = db.collection(`hospital_${hospitalId}_data`)
                       .doc('doctors')
                       .collection('doctors')
                       .doc(doctorId);
    
    await doctorRef.update({
      current_bookings: doctor.current_bookings,
      updated_at: new Date()
    });
    
    logger.info(`Updated booking count in Firebase for doctor ${doctorId} on ${date}: ${count}`);
  } catch (error) {
    logger.error(`Error updating booking count in Firebase:`, error);
    throw error;
  }
}



/**
 * Create a notification in Firebase
 * @param {string} hospitalId - Hospital ID
 * @param {string} notificationId - Notification ID (UUID)
 * @param {Object} notification - Notification data
 * @returns {Promise<void>}
 */
export const createNotification = async (hospitalId, notificationId, notification) => {
  try {
    const db = getFirestore();
    const notificationRef = db.collection(`hospital_${hospitalId}_data`)
                            .doc('notifications')
                            .collection('notifications')
                            .doc(notificationId);
    
    await notificationRef.set({
      ...notification,
      created_at: new Date(),
      updated_at: new Date()
    });
    
    logger.info(`Created notification ${notificationId} for hospital ${hospitalId}`);
  } catch (error) {
    logger.error(`Error creating notification:`, error);
    throw error;
  }
};

/**
 * Update notification status in Firebase
 * @param {string} hospitalId - Hospital ID
 * @param {string} notificationId - Notification ID
 * @param {Object} updates - Fields to update
 * @returns {Promise<void>}
 */
export const updateNotification = async (hospitalId, notificationId, updates) => {
  try {
    const db = getFirestore();
    const notificationRef = db.collection(`hospital_${hospitalId}_data`)
                            .doc('notifications')
                            .collection('notifications')
                            .doc(notificationId);
    
    await notificationRef.update({
      ...updates,
      updated_at: new Date()
    });
    
    logger.info(`Updated notification ${notificationId} for hospital ${hospitalId}`);
  } catch (error) {
    logger.error(`Error updating notification:`, error);
    throw error;
  }
};

/**
 * Get notification by ID
 * @param {string} hospitalId - Hospital ID
 * @param {string} notificationId - Notification ID
 * @returns {Promise<Object|null>} - Notification data
 */
export const getNotification = async (hospitalId, notificationId) => {
  try {
    const db = getFirestore();
    const notificationRef = db.collection(`hospital_${hospitalId}_data`)
                            .doc('notifications')
                            .collection('notifications')
                            .doc(notificationId);
    
    const doc = await notificationRef.get();
    
    if (!doc.exists) {
      logger.warn(`Notification ${notificationId} not found for hospital ${hospitalId}`);
      return null;
    }
    
    return { id: doc.id, ...doc.data() };
  } catch (error) {
    logger.error(`Error getting notification:`, error);
    throw error;
  }
};

export default {
  initializeFirebase,
  getFirestore,
  getDoctor,
  getDoctors,
  getTest,
  getTests,
  checkDoctorBookingLimit,
  updateBookingCountInFirebase,
  createNotification,
  updateNotification,
  getNotification
};
