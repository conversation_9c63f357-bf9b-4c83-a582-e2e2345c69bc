#!/usr/bin/env python3
"""
Interactive LLM Test

This script provides an interactive way to test the LLM service with
specific questions that require function calling.
"""

import asyncio
import sys
import os
from pathlib import Path
import json

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.test_config import create_test_context, setup_test_environment
from shared.llm_service import llm_service
from shared.function_handlers import get_function_handlers

class InteractiveLLMTester:
    """Interactive tester for LLM service."""
    
    def __init__(self):
        self.context = create_test_context()
        self.setup_complete = False
    
    async def setup(self):
        """Setup the LLM service for testing."""
        print("Setting up LLM service for interactive testing...")
        
        # Check environment
        env_status = setup_test_environment()
        if not env_status["ready"]:
            print("❌ Environment setup failed:")
            for issue in env_status["issues"]:
                print(f"   - {issue}")
            return False
        
        # Setup function handlers
        try:
            handlers = get_function_handlers()
            llm_service.set_function_handlers(handlers)
            print(f"✅ Setup complete with {len(handlers)} functions")
            self.setup_complete = True
            return True
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    async def test_specific_questions(self):
        """Test specific questions that require function calling."""
        if not self.setup_complete:
            print("❌ Setup not complete. Run setup() first.")
            return
        
        # Predefined test questions
        test_questions = [
            {
                "category": "Hospital Information",
                "questions": [
                    "What is the name of this hospital?",
                    "What are your hospital hours?",
                    "Tell me about your hospital services",
                    "What is your hospital address?"
                ]
            },
            {
                "category": "Doctor Inquiries", 
                "questions": [
                    "Who are the available doctors?",
                    "Show me all cardiologists",
                    "I need to see a heart specialist",
                    "Which doctors are available today?",
                    "Tell me about Dr. Smith"
                ]
            },
            {
                "category": "Medical Tests",
                "questions": [
                    "What medical tests do you offer?",
                    "I need a blood test",
                    "Show me all available tests",
                    "What is the cost of an X-ray?",
                    "How long does an ECG take?"
                ]
            },
            {
                "category": "Appointment Booking",
                "questions": [
                    "I want to book an appointment with Dr. Smith",
                    "Book me an appointment with a cardiologist",
                    "I need to schedule a doctor visit",
                    "Can I see Dr. Patel tomorrow?"
                ]
            },
            {
                "category": "Test Booking",
                "questions": [
                    "I want to book a blood test",
                    "Schedule an X-ray for me",
                    "I need to get an ECG done",
                    "Book a medical test for me"
                ]
            }
        ]
        
        print("\n" + "="*60)
        print("TESTING SPECIFIC QUESTIONS WITH FUNCTION CALLING")
        print("="*60)
        
        for category_data in test_questions:
            category = category_data["category"]
            questions = category_data["questions"]
            
            print(f"\n📋 Testing {category}:")
            print("-" * 40)
            
            for i, question in enumerate(questions, 1):
                print(f"\n{i}. Question: {question}")
                await self._test_single_question(question)
    
    async def _test_single_question(self, question: str):
        """Test a single question and show results."""
        try:
            result = await llm_service.process_message(question, self.context)
            
            if result.get("success"):
                print(f"✅ Response: {result.get('response', 'No response')[:200]}...")
                
                # Show function calls if any
                function_calls = result.get("function_calls", [])
                if function_calls:
                    print(f"🔧 Function calls made: {len(function_calls)}")
                    for call in function_calls:
                        print(f"   - {call.get('name', 'unknown')}({list(call.get('arguments', {}).keys())})")
                        if call.get('result'):
                            success = call['result'].get('success', False)
                            print(f"     Result: {'✅' if success else '❌'} {call['result'].get('message', '')}")
                else:
                    print("ℹ️  No function calls made")
            else:
                print(f"❌ Failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
    
    async def interactive_mode(self):
        """Interactive mode for custom questions."""
        if not self.setup_complete:
            print("❌ Setup not complete. Run setup() first.")
            return
        
        print("\n" + "="*60)
        print("INTERACTIVE MODE - Ask your own questions!")
        print("="*60)
        print("Type 'quit' to exit, 'help' for commands")
        print("Context: Test Hospital with sample doctors and tests")
        
        while True:
            try:
                question = input("\n🤔 Your question: ").strip()
                
                if question.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                elif question.lower() == 'help':
                    self._show_help()
                    continue
                elif question.lower() == 'context':
                    self._show_context()
                    continue
                elif not question:
                    continue
                
                print(f"\n🤖 Processing: {question}")
                await self._test_single_question(question)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def _show_help(self):
        """Show help information."""
        print("\n📖 Available commands:")
        print("  help     - Show this help")
        print("  context  - Show current context")
        print("  quit     - Exit interactive mode")
        print("\n💡 Try asking questions like:")
        print("  - 'Who are the available doctors?'")
        print("  - 'I want to book an appointment'")
        print("  - 'What tests do you offer?'")
        print("  - 'Tell me about your hospital'")
    
    def _show_context(self):
        """Show current context."""
        print("\n📋 Current Context:")
        print(f"  Hospital ID: {self.context.get('hospital_id')}")
        print(f"  Hospital Name: {self.context.get('hospital_name')}")
        print(f"  Language: {self.context.get('language')}")
        print(f"  User Phone: {self.context.get('user_info', {}).get('phone')}")

async def main():
    """Main function."""
    tester = InteractiveLLMTester()
    
    print("🚀 Starting Interactive LLM Tester")
    
    # Setup
    if not await tester.setup():
        print("❌ Setup failed. Exiting.")
        return 1
    
    # Show menu
    while True:
        print("\n" + "="*50)
        print("INTERACTIVE LLM TESTER MENU")
        print("="*50)
        print("1. Test specific predefined questions")
        print("2. Interactive mode (ask your own questions)")
        print("3. Exit")
        
        try:
            choice = input("\nSelect option (1-3): ").strip()
            
            if choice == '1':
                await tester.test_specific_questions()
            elif choice == '2':
                await tester.interactive_mode()
            elif choice == '3':
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please select 1, 2, or 3.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
