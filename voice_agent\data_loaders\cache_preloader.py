"""
Cache preloader for voice agent system.
Orchestrates data loading from Firebase and configuration files.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from .firebase_loader import FirebaseDataLoader
from .config_loader import ConfigDataLoader
from .query_generator import QueryGenerator

logger = logging.getLogger(__name__)


class CachePreloader:
    """
    Production-ready cache preloader that orchestrates data loading.
    Prioritizes Firebase data with configuration file fallback.
    """

    def __init__(self):
        """Initialize cache preloader with data loaders."""
        self.firebase_loader = FirebaseDataLoader()
        self.config_loader = ConfigDataLoader()
        self.query_generator = QueryGenerator(self.config_loader)

    async def preload_hospital_data_async(self, hospital_id: str, 
                                        hospital_data: Dict[str, Any] = None) -> bool:
        """
        Async preload hospital data into semantic cache.

        Args:
            hospital_id: Hospital identifier
            hospital_data: Optional pre-loaded hospital data

        Returns:
            bool: Success or failure
        """
        try:
            # Use provided data or load from sources
            if hospital_data:
                logger.info(f"Using provided hospital data for {hospital_id}")
                data = hospital_data
            else:
                data = await self._load_hospital_data(hospital_id)

            if not data or not data.get('doctors') and not data.get('tests'):
                logger.warning(f"No hospital data found for {hospital_id}")
                return False

            # Generate and cache queries
            success = await self._cache_generated_queries(hospital_id, data)
            
            if success:
                logger.info(f"Successfully preloaded cache for hospital {hospital_id}")
            else:
                logger.error(f"Failed to preload cache for hospital {hospital_id}")
            
            return success

        except Exception as e:
            logger.error(f"Error in async preload for hospital {hospital_id}: {e}")
            return False

    def preload_hospital_data_sync(self, hospital_id: str, 
                                 hospital_data: Dict[str, Any] = None) -> bool:
        """
        Synchronous preload hospital data into semantic cache.

        Args:
            hospital_id: Hospital identifier
            hospital_data: Optional pre-loaded hospital data

        Returns:
            bool: Success or failure
        """
        try:
            # Re-use running loop if already inside async context
            loop = asyncio.get_running_loop()
            # Use run_coroutine_threadsafe for running async code from sync context within existing loop
            future = asyncio.run_coroutine_threadsafe(
                self.preload_hospital_data_async(hospital_id, hospital_data), loop
            )
            return future.result()
        except RuntimeError:
            # Fallback for pure sync contexts (no running loop)
            return asyncio.run(self.preload_hospital_data_async(hospital_id, hospital_data))
        except Exception as e:
            logger.error(f"Error in sync preload for hospital {hospital_id}: {e}")
            return False

    async def _load_hospital_data(self, hospital_id: str) -> Dict[str, Any]:
        """
        Load hospital data with Firebase priority and config fallback.

        Args:
            hospital_id: Hospital identifier

        Returns:
            Dict containing hospital data
        """
        try:
            # Try Firebase first (priority)
            logger.info(f"Attempting to load hospital {hospital_id} from Firebase")
            firebase_data = await self.firebase_loader.load_hospital_data(hospital_id)
            
            if firebase_data and (firebase_data.get('doctors') or firebase_data.get('tests')):
                logger.info(f"Successfully loaded hospital {hospital_id} from Firebase")
                return firebase_data

            # Fallback to configuration files
            logger.info(f"Firebase data not available, trying configuration files for hospital {hospital_id}")
            config_data = self.config_loader.load_hospital_data(hospital_id)
            
            if config_data and (config_data.get('doctors') or config_data.get('tests')):
                logger.info(f"Successfully loaded hospital {hospital_id} from configuration")
                return config_data

            # No data found
            logger.warning(f"No data found for hospital {hospital_id} in Firebase or configuration")
            return {}

        except Exception as e:
            logger.error(f"Error loading hospital data for {hospital_id}: {e}")
            return {}

    async def _cache_generated_queries(self, hospital_id: str, 
                                     hospital_data: Dict[str, Any]) -> bool:
        """
        Generate and cache semantic queries for hospital data.

        Args:
            hospital_id: Hospital identifier
            hospital_data: Hospital data dictionary

        Returns:
            bool: Success or failure
        """
        try:
            # Import cache manager here to avoid circular imports
            from ..cache_manager import redis_manager
            
            if not redis_manager.is_connected():
                logger.error("Redis not connected, cannot cache queries")
                return False

            doctors = hospital_data.get('doctors', [])
            tests = hospital_data.get('tests', [])
            languages = hospital_data.get('languages', ['hi', 'bn', 'en'])

            total_cached = 0

            # Generate and cache doctor queries
            if doctors:
                doctor_queries = self.query_generator.generate_doctor_queries(doctors, languages)
                for query, response_data in doctor_queries:
                    try:
                        success = await redis_manager.cache_doctor_info_async(
                            query, response_data, hospital_id
                        )
                        if success:
                            total_cached += 1
                    except Exception as e:
                        logger.warning(f"Failed to cache doctor query '{query}': {e}")

            # Generate and cache test queries
            if tests:
                test_queries = self.query_generator.generate_test_queries(tests, languages)
                for query, response_data in test_queries:
                    try:
                        success = await redis_manager.cache_test_info_async(
                            query, response_data, hospital_id
                        )
                        if success:
                            total_cached += 1
                    except Exception as e:
                        logger.warning(f"Failed to cache test query '{query}': {e}")

            logger.info(f"Cached {total_cached} queries for hospital {hospital_id}")
            return total_cached > 0

        except Exception as e:
            logger.error(f"Error caching generated queries for hospital {hospital_id}: {e}")
            return False

    async def preload_all_hospitals(self) -> Dict[str, bool]:
        """
        Preload cache for all hospitals found in Firebase.

        Returns:
            Dict mapping hospital_id to success status
        """
        try:
            # Get all hospital IDs from Firebase
            hospital_ids = await self.firebase_loader.get_all_hospitals()
            
            if not hospital_ids:
                logger.warning("No hospitals found in Firebase")
                return {}

            results = {}
            
            # Preload each hospital
            for hospital_id in hospital_ids:
                try:
                    success = await self.preload_hospital_data_async(hospital_id)
                    results[hospital_id] = success
                    
                    if success:
                        logger.info(f"Successfully preloaded hospital {hospital_id}")
                    else:
                        logger.warning(f"Failed to preload hospital {hospital_id}")
                        
                except Exception as e:
                    logger.error(f"Error preloading hospital {hospital_id}: {e}")
                    results[hospital_id] = False

            successful = sum(1 for success in results.values() if success)
            total = len(results)
            logger.info(f"Preloaded {successful}/{total} hospitals successfully")
            
            return results

        except Exception as e:
            logger.error(f"Error in preload_all_hospitals: {e}")
            return {}

    def get_cache_statistics(self, hospital_id: str) -> Dict[str, Any]:
        """
        Get cache statistics for a hospital.

        Args:
            hospital_id: Hospital identifier

        Returns:
            Dict containing cache statistics
        """
        try:
            from ..cache_manager import redis_manager
            
            if not redis_manager.is_connected():
                return {"error": "Redis not connected"}

            # Count cached items for this hospital using production-safe SCAN
            doctor_keys = redis_manager.keys(f"semantic:{hospital_id}:doctors:*")
            test_keys = redis_manager.keys(f"semantic:{hospital_id}:tests:*")
            
            return {
                "hospital_id": hospital_id,
                "cached_doctor_queries": len(doctor_keys),
                "cached_test_queries": len(test_keys),
                "total_cached_queries": len(doctor_keys) + len(test_keys)
            }

        except Exception as e:
            logger.error(f"Error getting cache statistics for {hospital_id}: {e}")
            return {"error": str(e)}

    async def clear_hospital_cache(self, hospital_id: str) -> bool:
        """
        Clear all cached data for a hospital.

        Args:
            hospital_id: Hospital identifier

        Returns:
            bool: Success or failure
        """
        try:
            from ..cache_manager import redis_manager
            
            if not redis_manager.is_connected():
                logger.error("Redis not connected, cannot clear cache")
                return False

            # Clear semantic cache entries
            doctor_pattern = f"semantic:{hospital_id}:doctors:*"
            test_pattern = f"semantic:{hospital_id}:tests:*"

            doctor_deleted = await redis_manager.clear_cache_async(doctor_pattern)
            test_deleted = await redis_manager.clear_cache_async(test_pattern)
            
            total_deleted = doctor_deleted + test_deleted
            logger.info(f"Cleared {total_deleted} cache entries for hospital {hospital_id}")
            
            return total_deleted > 0

        except Exception as e:
            logger.error(f"Error clearing cache for hospital {hospital_id}: {e}")
            return False
