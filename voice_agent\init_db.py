import os
import psycopg2
import logging
from dotenv import load_dotenv

# Module-local logger – configuration stays with the host app
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

# Database connection details from environment variables
DATABASE_URL = os.environ.get("DATABASE_URL")

if not DATABASE_URL:
    logger.error("DATABASE_URL environment variable not set.")
    exit(1)

def create_tables():
    """Create tables in the PostgreSQL database"""
    commands = (
        """
        CREATE TABLE IF NOT EXISTS hospitals (
            id VARCHAR(50) PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            address TEXT,
            phone VARCHAR(50),
            email VARCHAR(255),
            settings JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS appointments (
            id VARCHAR(50) PRIMARY KEY,
            patient_id VARCHAR(50) NOT NULL,
            doctor_id VARCHAR(50) NOT NULL,
            hospital_id VARCHAR(50) NOT NULL,
            start_time TIMESTAMP WITH TIME ZONE NOT NULL,
            end_time TIMESTAMP WITH TIME ZONE NOT NULL,
            status VARCHAR(50) DEFAULT 'scheduled',
            notes TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS test_bookings (
            id SERIAL PRIMARY KEY,
            patient_name VARCHAR(255) NOT NULL,
            phone VARCHAR(50) NOT NULL,
            test_type_id VARCHAR(50) NOT NULL, -- Assuming test type ID comes from Firestore/external system
            hospital_id VARCHAR(50) NOT NULL, -- To associate with a specific hospital
            booking_time TIMESTAMP WITH TIME ZONE NOT NULL,
            status VARCHAR(50) DEFAULT 'scheduled',
            result_url TEXT,
            notes TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS call_records (
            id SERIAL PRIMARY KEY,
            call_id VARCHAR(255) UNIQUE NOT NULL, -- Unique identifier for the call
            caller_number VARCHAR(50) NOT NULL,
            hospital_id VARCHAR(50) NOT NULL,
            start_time TIMESTAMP WITH TIME ZONE NOT NULL,
            end_time TIMESTAMP WITH TIME ZONE,
            duration INTEGER, -- Duration in seconds
            recording_url TEXT, -- URL to the call recording
            transcript TEXT, -- Full transcript of the call
            status VARCHAR(50), -- e.g., completed, failed, in-progress
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
        """,


        """CREATE INDEX IF NOT EXISTS idx_test_bookings_hospital_id_time ON test_bookings(hospital_id, booking_time);""",
        """CREATE INDEX IF NOT EXISTS idx_call_records_hospital_id_start_time ON call_records(hospital_id, start_time);""",
        """CREATE INDEX IF NOT EXISTS idx_call_records_call_id ON call_records(call_id);""",
        """CREATE INDEX IF NOT EXISTS idx_appointments_hospital_id ON appointments(hospital_id);""",
        """CREATE INDEX IF NOT EXISTS idx_appointments_doctor_id ON appointments(doctor_id);""",
        """CREATE INDEX IF NOT EXISTS idx_appointments_start_time ON appointments(start_time);""",
        """CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);"""
    )
    conn = None
    try:
        # Connect to the PostgreSQL server
        logger.info('Connecting to the PostgreSQL database...')
        conn = psycopg2.connect(DATABASE_URL)
        cur = conn.cursor()
        # Create table one by one
        for command in commands:
            # Extract the first non-empty line for logging
            command_lines = [line.strip() for line in command.splitlines() if line.strip()]
            log_line = command_lines[0] if command_lines else command
            logger.info(f"Executing: {log_line}...")
            cur.execute(command)
        # Close communication with the PostgreSQL database server
        cur.close()
        # Commit the changes
        conn.commit()
        logger.info("Tables created successfully.")
    except (Exception, psycopg2.DatabaseError) as error:
        logger.error(error)
        if conn is not None:
            conn.rollback() # Rollback changes on error
    finally:
        if conn is not None:
            conn.close()
            logger.info('Database connection closed.')

if __name__ == '__main__':
    logger.info("Starting database initialization for voice agent...")
    create_tables()
    logger.info("Database initialization complete.")
    logger.info("You can now run the voice agent.")