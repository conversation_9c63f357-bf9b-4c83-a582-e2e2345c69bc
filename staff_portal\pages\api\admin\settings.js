import { withRole } from '../../../lib/auth';
import { getHospitalSettings, updateHospitalSettings } from '../../../lib/firebase';

// Only admin users can access hospital settings
export default withRole(async (req, res) => {
  // GET - Fetch hospital settings
  if (req.method === 'GET') {
    try {
      const { hospital_id } = req.query;
      
      // If hospital_id is not provided, use the authenticated user's hospital
      const hospitalId = hospital_id || req.user.hospital_id;
      
      // Verify hospital ID matches the authenticated user's hospital
      if (hospitalId !== req.user.hospital_id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to access settings for this hospital'
        });
      }
      
      // Get hospital settings from Firestore
      const result = await getHospitalSettings(hospitalId);
      
      if (result.success) {
        return res.status(200).json({
          success: true,
          data: result.data
        });
      } else {
        return res.status(404).json({
          success: false,
          message: 'Hospital settings not found'
        });
      }
    } catch (error) {
      console.error('Get hospital settings error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // PUT - Update hospital settings
  if (req.method === 'PUT') {
    try {
      const { hospitalId, settings } = req.body;
      
      // Validate required parameters
      if (!hospitalId || !settings) {
        return res.status(400).json({
          success: false,
          message: 'Hospital ID and settings are required'
        });
      }
      
      // Verify hospital ID matches the authenticated user's hospital
      if (hospitalId !== req.user.hospital_id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to update settings for this hospital'
        });
      }
      
      // Update hospital settings in Firestore
      const result = await updateHospitalSettings(hospitalId, settings);
      
      if (result.success) {
        return res.status(200).json({
          success: true,
          message: 'Hospital settings updated successfully'
        });
      } else {
        return res.status(500).json({
          success: false,
          message: result.error || 'Failed to update hospital settings'
        });
      }
    } catch (error) {
      console.error('Update hospital settings error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // Method not allowed
  return res.status(405).json({
    success: false,
    message: 'Method not allowed'
  });
}, ['admin']); // Only admin role can access this endpoint