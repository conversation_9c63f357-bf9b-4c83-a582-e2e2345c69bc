/**
 * OCR module for WhatsApp Agent
 * 
 * Uses Google Cloud Vision API to process prescription images
 * and extract text for further analysis
 */

import { ImageAnnotatorClient } from '@google-cloud/vision';
import axios from 'axios';
import { logger } from './logger.js';

// Initialize Google Cloud Vision client
let visionClient = null;

/**
 * Get or initialize the Vision API client
 * @returns {ImageAnnotatorClient} - Vision API client
 */
const getVisionClient = () => {
  if (!visionClient) {
    try {
      // Use the credentials from the environment variable
      visionClient = new ImageAnnotatorClient();
      logger.info('Google Cloud Vision client initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Google Cloud Vision client:', error);
      throw error;
    }
  }
  return visionClient;
};

/**
 * Download image from URL to buffer
 * @param {string} imageUrl - URL of the image
 * @returns {Promise<Buffer>} - Image buffer
 */
const downloadImage = async (imageUrl) => {
  try {
    const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    return Buffer.from(response.data, 'binary');
  } catch (error) {
    logger.error(`Error downloading image from ${imageUrl}:`, error);
    throw error;
  }
};

/**
 * Process an image URL with OCR
 * @param {string} imageUrl - URL of the image to process
 * @returns {Promise<Object>} - Extracted text and confidence scores
 */
export const processImage = async (imageUrl) => {
  try {
    logger.info(`Processing image with OCR: ${imageUrl}`);
    
    // Download the image
    const imageBuffer = await downloadImage(imageUrl);
    
    // Get Vision client
    const client = getVisionClient();
    
    // Call Google Cloud Vision API
    const [result] = await client.textDetection(imageBuffer);
    const detections = result.textAnnotations;
    
    if (!detections || detections.length === 0) {
      logger.warn('No text detected in image');
      return {
        success: false,
        error: 'No text detected in image',
        text: '',
        confidence: 0
      };
    }
    
    // Extract full text (first element contains all text)
    const fullText = detections[0].description;
    
    // Calculate overall confidence score based on individual word detections
    let totalConfidence = 0;
    let wordCount = 0;
    
    if (detections.length > 1) {
      for (let i = 1; i < detections.length; i++) {
        if (detections[i].confidence) {
          totalConfidence += detections[i].confidence;
          wordCount++;
        }
      }
    }
    
    const averageConfidence = wordCount > 0 ? totalConfidence / wordCount : 0.5;
    
    return {
      success: true,
      text: fullText,
      confidence: averageConfidence,
      detections: detections.slice(1)  // Skip the first one as it's the full text
    };
  } catch (error) {
    logger.error('Error processing image with OCR:', error);
    return {
      success: false,
      error: error.message,
      text: '',
      confidence: 0
    };
  }
};

/**
 * Extract prescription details from OCR text
 * @param {string} text - OCR extracted text
 * @returns {Object} - Extracted test details
 */
export const extractPrescriptionDetails = async (text) => {
  try {
    // Extract test names using regex patterns
    // Scan for common test patterns in prescriptions
    const testPatterns = [
      /(?:test|exam|scan|x-ray|ultrasound|mri|ct scan|blood test|urine test)s?:?\s*([^.;]*)[.;]/i,
      /(?:prescribe|recommend|order)(?:d|ing)?:?\s*([^.;]*(?:test|exam|scan|x-ray|ultrasound|mri|ct)[^.;]*)[.;]/i,
      /(?:lab(?:oratory)?|diagnostic|pathology):?\s*([^.;]*)[.;]/i
    ];
    
    // Extract dates
    const datePatterns = [
      /(?:date|on|scheduled for):?\s*(\d{1,2}[-/\.]\d{1,2}[-/\.]\d{2,4})/i,
      /(\d{1,2}(?:st|nd|rd|th)?\s+(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\.?\s+\d{2,4})/i,
      /(\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2})/i  // YYYY-MM-DD format
    ];
    
    // Extract patient name
    const namePatterns = [
      /(?:patient|name|patient name|patient's name):?\s*([A-Z][a-z]+(?: [A-Z][a-z]+)+)/i,
      /(?:mr|mrs|ms|miss|dr)\.?\s+([A-Z][a-z]+(?: [A-Z][a-z]+)+)/i
    ];
    
    // Try to extract tests
    let tests = null;
    for (const pattern of testPatterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        tests = match[1].trim();
        break;
      }
    }
    
    // Try to extract date
    let date = null;
    for (const pattern of datePatterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        date = match[1].trim();
        break;
      }
    }
    
    // Try to extract patient name
    let patientName = null;
    for (const pattern of namePatterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        patientName = match[1].trim();
        break;
      }
    }
    
    return {
      success: !!(tests || date || patientName),
      tests,
      date,
      patientName,
      fullText: text
    };
  } catch (error) {
    logger.error('Error extracting prescription details:', error);
    return {
      success: false,
      error: error.message,
      fullText: text
    };
  }
};

export default {
  processImage,
  extractPrescriptionDetails
};
