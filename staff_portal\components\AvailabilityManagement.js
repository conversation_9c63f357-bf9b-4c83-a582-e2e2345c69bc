import React, { useState, useEffect } from 'react';
import { Calendar, Users, Activity, RefreshCw, Save, AlertCircle, CheckCircle, XCircle, Clock } from 'react-feather';
import { useNotifications } from '../hooks/useNotifications';
import { NotificationContainer } from './notifications/NotificationContainer';

/**
 * Availability Management Component
 * Allows admin and receptionist to manage doctor and test availability for specific dates
 */
export function AvailabilityManagement({ hospitalId, userRole }) {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [doctors, setDoctors] = useState([]);
  const [tests, setTests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [changes, setChanges] = useState(new Map());
  const [redisStatus, setRedisStatus] = useState(null);
  const [lastSyncTime, setLastSyncTime] = useState(null);

  // Notification system
  const {
    notifications,
    removeNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo
  } = useNotifications();

  // Load availability data when date changes
  useEffect(() => {
    if (selectedDate) {
      loadAvailabilityData();
    }
  }, [selectedDate, hospitalId]);

  // Load availability data for the selected date
  const loadAvailabilityData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/availability?date=${selectedDate}`);
      const result = await response.json();
      
      if (result.success) {
        setDoctors(result.data.doctors || []);
        setTests(result.data.tests || []);
        setRedisStatus(result.redisConnected);
        setChanges(new Map()); // Clear changes when loading new data

        // Show success message if this is a manual refresh
        if (doctors.length > 0 || tests.length > 0) {
          showInfo('Data Refreshed', 'Availability data has been updated successfully');
        }
      } else {
        console.error('Failed to load availability data:', result.message);
        showError(
          'Failed to Load Data',
          result.message || 'Unable to load availability data. Please try again.'
        );
      }
    } catch (error) {
      handleNetworkError(error, 'load');
    } finally {
      setLoading(false);
    }
  };

  // Toggle availability for a doctor or test
  const toggleAvailability = (id, type, currentStatus) => {
    const newStatus = !currentStatus;
    const key = `${type}_${id}`;
    
    // Update local state
    if (type === 'doctor') {
      setDoctors(prev => prev.map(doctor => 
        doctor.id === id ? { ...doctor, available: newStatus } : doctor
      ));
    } else {
      setTests(prev => prev.map(test => 
        test.id === id ? { ...test, available: newStatus } : test
      ));
    }
    
    // Track changes
    setChanges(prev => {
      const newChanges = new Map(prev);
      newChanges.set(key, { id, type, available: newStatus });
      return newChanges;
    });
  };

  // Check if there are unsaved changes
  const hasChanges = () => changes.size > 0;

  // Save all changes
  const saveChanges = async () => {
    if (!hasChanges()) return;
    
    try {
      setSaving(true);
      
      const updates = Array.from(changes.values());
      
      const response = await fetch('/api/availability', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          date: selectedDate,
          updates
        }),
      });
      
      const result = await response.json();
      
      if (result.success) {
        setChanges(new Map()); // Clear changes after successful save
        setLastSyncTime(new Date().toISOString());

        // Update Redis status based on sync result
        if (result.data.syncStatus) {
          setRedisStatus(result.data.syncStatus.status === 'success');

          // Show appropriate message based on sync status
          if (result.data.syncStatus.status === 'success') {
            showSuccess(
              'Changes Saved Successfully',
              'Availability updated and synced with voice agent'
            );
          } else {
            showWarning(
              'Changes Saved with Warning',
              'Availability updated but voice agent sync failed. Voice calls may show outdated information.'
            );
          }
        } else {
          showSuccess('Changes Saved', 'Availability has been updated successfully');
        }

        console.log('Availability updated successfully:', result.data);
      } else {
        console.error('Failed to save availability changes:', result.message);
        showError(
          'Failed to Save Changes',
          result.message || 'Unable to save availability changes. Please try again.'
        );
      }
    } catch (error) {
      handleNetworkError(error, 'save');
    } finally {
      setSaving(false);
    }
  };

  // Reset changes
  const resetChanges = () => {
    setChanges(new Map());
    loadAvailabilityData(); // Reload original data
    showInfo('Changes Reset', 'All unsaved changes have been discarded');
  };

  // Handle network errors with retry option
  const handleNetworkError = (error, context) => {
    console.error(`Error in ${context}:`, error);

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      showError(
        'Connection Error',
        'Unable to connect to the server. Please check your internet connection.',
        {
          duration: 10000, // Show longer for network errors
          onClick: () => {
            if (context === 'load') {
              loadAvailabilityData();
            } else if (context === 'save') {
              saveChanges();
            }
          }
        }
      );
    } else {
      showError(
        'Unexpected Error',
        `An unexpected error occurred while trying to ${context} data. Please try again.`
      );
    }
  };

  // Get formatted date for display
  const getFormattedDate = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  // Get tomorrow's date for default selection
  const getTomorrowDate = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  };

  if (loading && doctors.length === 0 && tests.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin mr-2" />
        <span>Loading availability data...</span>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Activity className="w-5 h-5 text-green-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Availability Management</h3>
          </div>
          
          {/* Status Indicators */}
          <div className="flex items-center space-x-4">
            {/* Redis Status */}
            <div className="flex items-center space-x-2">
              {redisStatus === true && (
                <div className="flex items-center text-green-600">
                  <CheckCircle className="w-4 h-4 mr-1" />
                  <span className="text-sm">Voice Agent Synced</span>
                </div>
              )}
              {redisStatus === false && (
                <div className="flex items-center text-red-600">
                  <XCircle className="w-4 h-4 mr-1" />
                  <span className="text-sm">Voice Agent Not Synced</span>
                </div>
              )}
              {redisStatus === null && (
                <div className="flex items-center text-gray-500">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  <span className="text-sm">Sync Status Unknown</span>
                </div>
              )}
            </div>
            
            {/* Last Sync Time */}
            {lastSyncTime && (
              <div className="flex items-center text-gray-500 text-sm">
                <Clock className="w-4 h-4 mr-1" />
                <span>Last sync: {new Date(lastSyncTime).toLocaleTimeString()}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Date Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Date
          </label>
          <div className="flex items-center space-x-4">
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              min={new Date().toISOString().split('T')[0]} // Today or later
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={loading || saving}
            />
            <button
              onClick={() => setSelectedDate(getTomorrowDate())}
              className="px-3 py-2 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
              disabled={loading || saving}
            >
              Tomorrow
            </button>
            <span className="text-sm text-gray-600">
              {getFormattedDate(selectedDate)}
            </span>
          </div>
        </div>

        {/* Doctors Section */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <Users className="w-5 h-5 text-blue-600 mr-2" />
            <h4 className="text-lg font-medium text-gray-900">Doctors ({doctors.length})</h4>
          </div>
          
          {doctors.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {doctors.map(doctor => (
                <div key={doctor.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h5 className="font-medium text-gray-900">{doctor.name}</h5>
                      <p className="text-sm text-gray-600">{doctor.specialty}</p>
                    </div>
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={doctor.available}
                        onChange={() => toggleAvailability(doctor.id, 'doctor', doctor.available)}
                        disabled={loading || saving}
                        className="sr-only"
                      />
                      <div className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        doctor.available ? 'bg-green-600' : 'bg-gray-300'
                      }`}>
                        <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          doctor.available ? 'translate-x-6' : 'translate-x-1'
                        }`} />
                      </div>
                    </label>
                  </div>
                  <div className="mt-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      doctor.available 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {doctor.available ? 'Available' : 'Unavailable'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No doctors found for this hospital</p>
            </div>
          )}
        </div>

        {/* Tests Section */}
        <div className="mb-6">
          <div className="flex items-center mb-4">
            <Activity className="w-5 h-5 text-purple-600 mr-2" />
            <h4 className="text-lg font-medium text-gray-900">Tests ({tests.length})</h4>
          </div>
          
          {tests.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {tests.map(test => (
                <div key={test.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h5 className="font-medium text-gray-900">{test.name}</h5>
                      <p className="text-sm text-gray-600">₹{test.cost}</p>
                    </div>
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={test.available}
                        onChange={() => toggleAvailability(test.id, 'test', test.available)}
                        disabled={loading || saving}
                        className="sr-only"
                      />
                      <div className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        test.available ? 'bg-green-600' : 'bg-gray-300'
                      }`}>
                        <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          test.available ? 'translate-x-6' : 'translate-x-1'
                        }`} />
                      </div>
                    </label>
                  </div>
                  <div className="mt-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      test.available 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {test.available ? 'Available' : 'Unavailable'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Activity className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No tests found for this hospital</p>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <div className="flex items-center space-x-3">
            <button
              onClick={resetChanges}
              disabled={!hasChanges() || saving}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Reset
            </button>
            
            <button
              onClick={loadAvailabilityData}
              disabled={loading || saving}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <RefreshCw className={`w-4 h-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>

          <div className="flex items-center space-x-3">
            {hasChanges() && (
              <span className="text-sm text-orange-600">
                {changes.size} unsaved change{changes.size !== 1 ? 's' : ''}
              </span>
            )}
            
            <button
              onClick={saveChanges}
              disabled={!hasChanges() || saving}
              className="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {saving ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </div>
      </div>
      </div>

      {/* Notification Container */}
      <NotificationContainer
        notifications={notifications}
        onRemove={removeNotification}
      />
    </>
  );
}

export default AvailabilityManagement;
