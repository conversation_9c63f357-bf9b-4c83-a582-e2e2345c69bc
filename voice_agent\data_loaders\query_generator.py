"""
Dynamic query generator for voice agent system.
Generates semantic queries from hospital data using predefined templates.
"""

import logging
import re
from typing import Dict, Any, List, Tuple, Optional
from .config_loader import ConfigDataLoader

logger = logging.getLogger(__name__)


class QueryGenerator:
    """
    Production-ready query generator for semantic caching.
    Generates multilingual queries from hospital data using templates.
    """

    def __init__(self, config_loader: ConfigDataLoader = None):
        """
        Initialize query generator.
        
        Args:
            config_loader: Configuration data loader instance
        """
        self.config_loader = config_loader or ConfigDataLoader()
        self.query_patterns = self.config_loader.load_query_patterns()
        self.language_templates = {}
        
        # Load language templates for supported languages
        self._load_language_templates()

    def _load_language_templates(self):
        """Load templates for all supported languages."""
        try:
            supported_languages = self.config_loader.get_supported_languages()

            for language in supported_languages:
                try:
                    templates = self.config_loader.load_language_templates(language)
                    if templates:
                        self.language_templates[language] = templates
                        logger.info(f"Loaded templates for language: {language}")
                    else:
                        logger.warning(f"No templates found for language: {language}")
                except (FileNotFoundError, PermissionError) as e:
                    logger.warning(f"Could not load templates for language {language}: {e}")
                    continue
                except (ValueError, KeyError) as e:
                    logger.error(f"Invalid template format for language {language}: {e}")
                    continue
                except Exception as e:
                    logger.error(f"Unexpected error loading templates for language {language}: {e}")
                    continue

            if not self.language_templates:
                logger.warning("No language templates loaded, using default patterns")

        except Exception as e:
            logger.error(f"Critical error in language template loading: {e}")
            # Re-raise critical errors that prevent system initialization
            raise

    def generate_doctor_queries(self, doctors: List[Dict[str, Any]],
                              languages: List[str] = None) -> List[Tuple[str, Dict[str, Any]]]:
        """
        Generate semantic queries for doctors.

        Args:
            doctors: List of doctor data dictionaries
            languages: List of language codes to generate queries for

        Returns:
            List of (query, response_data) tuples
        """
        if not doctors:
            return []

        if languages is None:
            languages = ["hi", "bn", "en"]  # Default languages

        queries = []

        for doctor in doctors:
            try:
                doctor_name = doctor.get('name', 'Unknown Doctor')
                specialty = doctor.get('specialty', 'General Medicine')
                schedule = doctor.get('schedule', 'Contact hospital for schedule')

                # Generate queries for each language
                for language in languages:
                    # Generate name-based queries
                    name_queries = self._generate_name_queries(doctor_name, doctor, language)
                    queries.extend(name_queries)

                    # Generate specialty-based queries
                    specialty_queries = self._generate_specialty_queries(specialty, doctor, language)
                    queries.extend(specialty_queries)

            except Exception as e:
                logger.error(f"Error generating queries for doctor {doctor.get('name', '<unknown>')}: {e}")
                continue

        logger.info(f"Generated {len(queries)} doctor queries for {len(doctors)} doctors")
        return queries

    def generate_test_queries(self, tests: List[Dict[str, Any]],
                            languages: List[str] = None) -> List[Tuple[str, Dict[str, Any]]]:
        """
        Generate semantic queries for tests.

        Args:
            tests: List of test data dictionaries
            languages: List of language codes to generate queries for

        Returns:
            List of (query, response_data) tuples
        """
        if not tests:
            return []

        if languages is None:
            languages = ["hi", "bn", "en"]  # Default languages

        queries = []

        for test in tests:
            try:
                test_name = test.get('name', 'Unknown Test')
                price = test.get('price', 'Contact hospital for pricing')
                duration = test.get('duration', 'Contact hospital for duration')

                # Generate queries for each language
                for language in languages:
                    # Generate name-based queries
                    name_queries = self._generate_test_name_queries(test_name, test, language)
                    queries.extend(name_queries)

                    # Generate general test queries
                    general_queries = self._generate_general_test_queries(test, language)
                    queries.extend(general_queries)

            except Exception as e:
                logger.error(f"Error generating queries for test {test.get('name', '<unknown>')}: {e}")
                continue

        logger.info(f"Generated {len(queries)} test queries for {len(tests)} tests")
        return queries

    def _generate_name_queries(self, doctor_name: str, doctor_data: Dict[str, Any], 
                             language: str) -> List[Tuple[str, Dict[str, Any]]]:
        """Generate name-based queries for a doctor."""
        queries = []
        
        try:
            # Get language-specific templates
            templates = self.language_templates.get(language, {})
            doctor_templates = templates.get('doctor_name_queries', [])
            
            # If no language-specific templates, use default patterns
            if not doctor_templates:
                doctor_templates = self.query_patterns.get('doctor_patterns', {}).get('name_query', [])
            
            # Clean doctor name for query generation
            clean_name = self._clean_doctor_name(doctor_name)
            
            for template in doctor_templates:
                try:
                    # Replace placeholders in template
                    query = template.format(
                        doctor_name=clean_name,
                        name=clean_name
                    )
                    
                    # Create response data
                    response_data = {
                        "name": doctor_data.get('name', doctor_name),
                        "specialty": doctor_data.get('specialty', 'General Medicine'),
                        "schedule": doctor_data.get('schedule', 'Contact hospital for schedule'),
                        "id": doctor_data.get('id', ''),
                        "language": language
                    }
                    
                    queries.append((query, response_data))
                    
                except (KeyError, ValueError) as e:
                    logger.warning(f"Error formatting template '{template}' for doctor {doctor_name}: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"Error generating name queries for doctor {doctor_name}: {e}")
        
        return queries

    def _generate_specialty_queries(self, specialty: str, doctor_data: Dict[str, Any], 
                                  language: str) -> List[Tuple[str, Dict[str, Any]]]:
        """Generate specialty-based queries for a doctor."""
        queries = []
        
        try:
            # Get language-specific templates
            templates = self.language_templates.get(language, {})
            specialty_templates = templates.get('doctor_specialty_queries', [])
            
            # If no language-specific templates, use default patterns
            if not specialty_templates:
                specialty_templates = self.query_patterns.get('doctor_patterns', {}).get('specialty_query', [])
            
            for template in specialty_templates:
                try:
                    # Replace placeholders in template
                    query = template.format(
                        specialty=specialty,
                        doctor_specialty=specialty
                    )
                    
                    # Create response data
                    response_data = {
                        "name": doctor_data.get('name', 'Unknown Doctor'),
                        "specialty": specialty,
                        "schedule": doctor_data.get('schedule', 'Contact hospital for schedule'),
                        "id": doctor_data.get('id', ''),
                        "language": language
                    }
                    
                    queries.append((query, response_data))
                    
                except (KeyError, ValueError) as e:
                    logger.warning(f"Error formatting template '{template}' for specialty {specialty}: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"Error generating specialty queries for {specialty}: {e}")
        
        return queries

    def _generate_test_name_queries(self, test_name: str, test_data: Dict[str, Any], 
                                  language: str) -> List[Tuple[str, Dict[str, Any]]]:
        """Generate name-based queries for a test."""
        queries = []
        
        try:
            # Get language-specific templates
            templates = self.language_templates.get(language, {})
            test_templates = templates.get('test_name_queries', [])
            
            # If no language-specific templates, use default patterns
            if not test_templates:
                test_templates = self.query_patterns.get('test_patterns', {}).get('name_query', [])
            
            for template in test_templates:
                try:
                    # Replace placeholders in template
                    query = template.format(
                        test_name=test_name,
                        name=test_name
                    )
                    
                    # Create response data
                    response_data = {
                        "name": test_name,
                        "price": test_data.get('price', 'Contact hospital for pricing'),
                        "duration": test_data.get('duration', 'Contact hospital for duration'),
                        "id": test_data.get('id', ''),
                        "language": language
                    }
                    
                    queries.append((query, response_data))
                    
                except (KeyError, ValueError) as e:
                    logger.warning(f"Error formatting template '{template}' for test {test_name}: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"Error generating name queries for test {test_name}: {e}")
        
        return queries

    def _generate_general_test_queries(self, test_data: Dict[str, Any],
                                     language: str) -> List[Tuple[str, Dict[str, Any]]]:
        """Generate general queries for a test."""
        queries = []

        try:
            # Get language-specific templates
            templates = self.language_templates.get(language, {})
            general_templates = templates.get('test_general_queries', [])

            # If no language-specific templates, use default patterns
            if not general_templates:
                general_templates = self.query_patterns.get('test_patterns', {}).get('general_query', [])

            for template in general_templates:
                try:
                    # Fill in any optional placeholders first
                    query = template.format(
                        test_name=test_data.get('name', 'Unknown Test'),
                        name=test_data.get('name', 'Unknown Test')
                    )

                    # Create response data
                    response_data = {
                        "name": test_data.get('name', 'Unknown Test'),
                        "price": test_data.get('price', 'Contact hospital for pricing'),
                        "duration": test_data.get('duration', 'Contact hospital for duration'),
                        "id": test_data.get('id', ''),
                        "language": language
                    }

                    queries.append((query, response_data))

                except (KeyError, ValueError) as e:
                    logger.warning(f"Error formatting general template '{template}' for test {test_data.get('name', '<unknown>')}: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error generating general test queries: {e}")

        return queries

    def _clean_doctor_name(self, name: str) -> str:
        """Clean doctor name for query generation."""
        if not name:
            return "doctor"
        
        # Remove common prefixes like "Dr.", "Dr ", "DR.", etc.
        cleaned = re.sub(r"(?i)^dr\.?\s*", "", name).strip()
        
        # If name becomes empty after cleaning, return original
        if not cleaned:
            return name
        
        return cleaned
