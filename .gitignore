# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
venv/
ENV/
env/
.venv/

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
.npm
.yarn-integrity

# Next.js
.next/
out/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.history

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Firebase/Google Cloud
*-firebase-adminsdk-*.json
*.json.key
firebase-debug.log
firebase-debug.*.log
*-sa-*.json
*.serviceaccount.json
*-service-account-*.json

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Documentation
ASYNC_OPENAI_IMPROVEMENTS.md
ASYNC_REDIS_IMPROVEMENTS.md
CACHE_IMPROVEMENTS.md
CACHE_KEY_NORMALIZATION_FIX.md
HINDI_PRIMARY_LANGUAGE_CHANGES.md
PRODUCTION_SEMANTIC_SOLUTION.md
SEMANTIC_SETUP_GUIDE.md
FIREBASE_SEMANTIC_CACHE_IMPLEMENTATION.md
NORMALIZATION_CONSOLIDATION_SUMMARY.md

# Testing and Demo Files
test_async_openai.py
test_async_redis.py
test_cache_performance.py
test_cache_key_normalization.py
demo_async_improvement.py
demo_cache_key_fix.py
performance_comparison.py
simple_validation.py
validate_async_implementation.py
test_normalization_consolidation.py

tests
Docs