# Voice Agent Dynamic Data Loading System

## Overview

The Voice Agent Dynamic Data Loading System is a comprehensive, production-ready solution that eliminates hardcoded values from the voice agent system. It provides a hybrid approach with Firebase as the primary data source and JSON configuration files as fallback, ensuring zero hardcoded data in production code.

## 🚀 Key Features

### 1. **Zero Hardcoded Values**
- All hospital data loaded dynamically from Firebase or configuration files
- No hardcoded doctor names, test prices, or schedules in code
- Production-ready with proper error handling and fallbacks

### 2. **Hybrid Data Loading (Plan B)**
- **Primary**: Firebase Firestore integration
- **Fallback**: JSON configuration files
- Automatic failover when Firebase is unavailable
- Seamless integration with existing Firebase structure

### 3. **Multilingual Query Generation**
- Predefined templates for Hindi, Bengali, and English
- Dynamic query generation from real hospital data
- Language-specific query patterns and translations
- Extensible to support additional Indian regional languages

### 4. **Production-Ready Architecture**
- Async/await support for high-concurrency scenarios
- Comprehensive error handling and logging
- Redis caching integration
- Performance optimized for sub-100ms response times

### 5. **Extensible Configuration System**
- JSON-based configuration files
- Hospital-specific overrides
- Language-specific templates
- Easy addition of new hospitals and languages

## 📁 Directory Structure

```
voice_agent/
├── data_loaders/
│   ├── __init__.py                 # Package initialization
│   ├── firebase_loader.py          # Firebase data loader
│   ├── config_loader.py            # Configuration file loader
│   ├── query_generator.py          # Dynamic query generation
│   ├── cache_preloader.py          # Cache orchestration
│   └── README.md                   # This documentation
├── config/
│   ├── hospital_templates/
│   │   ├── default.json            # Default hospital template
│   │   ├── hospital_456.json       # Hospital-specific config
│   │   └── query_patterns.json     # Query generation patterns
│   └── languages/
│       ├── query_templates_hi.json # Hindi query templates
│       ├── query_templates_bn.json # Bengali query templates
│       └── query_templates_en.json # English query templates
└── cache_manager.py                # Updated with new methods
```

## 🔧 Components

### 1. FirebaseDataLoader
- Loads hospital data from Firebase Firestore
- Handles doctors, tests, and hospital information
- Async operations for high performance
- Automatic retry logic and error handling

### 2. ConfigDataLoader  
- Loads data from JSON configuration files
- Hospital-specific and default templates
- Language template management
- Fallback when Firebase is unavailable

### 3. QueryGenerator
- Generates semantic queries from hospital data
- Uses predefined templates for consistency
- Multilingual support with language-specific patterns
- Dynamic placeholder replacement

### 4. CachePreloader
- Orchestrates the entire data loading process
- Prioritizes Firebase over configuration files
- Manages semantic cache population
- Provides statistics and cache management

## 🚀 Getting Started

### Step 1: Installation
The system is already integrated into the voice agent. No additional installation required.

### Step 2: Firebase Setup (Primary Data Source)
Ensure your Firebase has the following structure:
```
hospitals/
  {hospital_id}/
    name: "Hospital Name"
    languages: ["hi", "bn", "en"]

hospital_{hospital_id}_data/
  doctors/
    doctors/
      {doctor_id}/
        name: "Dr. Name"
        specialty: "Specialty"
        schedule: "Schedule"
  test_info/
    tests/
      {test_id}/
        name: "Test Name"
        price: "₹500"
        duration: "30 minutes"
```

### Step 3: Configuration Files (Fallback)
Create hospital-specific configurations in `voice_agent/config/hospital_templates/`:

```json
{
  "hospital_info": {
    "id": "123",
    "name": "Your Hospital",
    "languages": ["hi", "bn", "en"]
  },
  "doctors": [...],
  "tests": [...]
}
```

### Step 4: Language Templates
Customize language templates in `voice_agent/config/languages/` for your specific needs.

## 💻 Usage Examples

### Basic Usage
```python
from voice_agent.data_loaders import CachePreloader

# Initialize preloader
preloader = CachePreloader()

# Preload single hospital (async)
success = await preloader.preload_hospital_data_async("hospital_456")

# Preload single hospital (sync)
success = preloader.preload_hospital_data_sync("hospital_456")

# Preload all hospitals
results = await preloader.preload_all_hospitals()
```

### Using with Existing Cache Manager
```python
from voice_agent.cache_manager import redis_manager

# Use the new method (recommended)
success = await redis_manager.preload_hospital_data_from_config_async(
    hospital_id="456",
    hospital_data=custom_data  # Optional
)
```

### Custom Data Loading
```python
from voice_agent.data_loaders import FirebaseDataLoader, ConfigDataLoader

# Load from Firebase
firebase_loader = FirebaseDataLoader()
hospital_data = await firebase_loader.load_hospital_data("456")

# Load from configuration
config_loader = ConfigDataLoader()
hospital_data = config_loader.load_hospital_data("456")
```

## 🔧 Configuration

### Hospital Templates
Create `voice_agent/config/hospital_templates/hospital_{id}.json`:
```json
{
  "hospital_info": {
    "id": "456",
    "name": "Apollo Hospital",
    "languages": ["hi", "bn", "en"]
  },
  "doctors": [
    {
      "id": "doc_001",
      "name": "Dr. Rajesh Kumar", 
      "specialty": "Cardiology",
      "schedule": "Monday to Friday, 10 AM to 4 PM"
    }
  ],
  "tests": [
    {
      "id": "test_001",
      "name": "Blood Test",
      "price": "₹400",
      "duration": "30 minutes"
    }
  ]
}
```

### Language Templates
Create `voice_agent/config/languages/query_templates_{lang}.json`:
```json
{
  "language": "hi",
  "doctor_name_queries": [
    "डॉक्टर {doctor_name} कब आते हैं",
    "{doctor_name} डॉक्टर का समय क्या है"
  ],
  "test_name_queries": [
    "{test_name} की कीमत क्या है",
    "{test_name} कितने का है"
  ]
}
```

## 🔍 Monitoring and Statistics

### Cache Statistics
```python
# Get cache statistics for a hospital
stats = preloader.get_cache_statistics("456")
print(f"Cached queries: {stats['total_cached_queries']}")

# Clear hospital cache
success = await preloader.clear_hospital_cache("456")
```

### Logging
The system provides comprehensive logging:
- Data loading progress
- Query generation statistics  
- Error handling and fallbacks
- Performance metrics

## 🛠️ Advanced Features

### Custom Query Patterns
Modify `voice_agent/config/hospital_templates/query_patterns.json` to add new query patterns:
```json
{
  "doctor_patterns": {
    "name_query": [
      "when does {doctor_name} come",
      "custom pattern for {doctor_name}"
    ]
  }
}
```

### Adding New Languages
1. Create `query_templates_{lang}.json` in `voice_agent/config/languages/`
2. Add language code to hospital configurations
3. Update language_config.py if needed

### Hospital-Specific Overrides
Create hospital-specific files to override default templates:
- `hospital_456.json` - Complete hospital configuration
- Inherits from `default.json` for missing fields

## 🔒 Production Considerations

### Performance
- Async operations for high concurrency
- Redis caching for fast query responses
- Lazy loading of language templates
- Optimized for sub-100ms response times

### Error Handling
- Graceful fallback from Firebase to configuration files
- Comprehensive logging for debugging
- Retry logic for transient failures
- Default templates when data is missing

### Scalability
- Supports unlimited hospitals
- Easy addition of new languages
- Modular architecture for extensions
- Efficient memory usage

## 🐛 Troubleshooting

### Common Issues

1. **Firebase Connection Failed**
   - Check Firebase credentials
   - Verify network connectivity
   - System will fallback to configuration files

2. **No Configuration Found**
   - Ensure `default.json` exists
   - Check hospital-specific configuration files
   - Verify file permissions

3. **Query Generation Failed**
   - Check language template files
   - Verify query pattern syntax
   - Review placeholder names

### Debug Mode
Enable debug logging:
```python
import logging
logging.getLogger('voice_agent.data_loaders').setLevel(logging.DEBUG)
```

## 📈 Migration Guide

### From Hardcoded Data
1. Replace calls to old `preload_hospital_data` methods
2. Use new `preload_hospital_data_from_config_async` method
3. Create configuration files for your hospitals
4. Test with Firebase and configuration fallback

### Backward Compatibility
- Old methods are deprecated but still work
- Gradual migration supported
- No breaking changes to existing APIs

## 🤝 Contributing

### Adding New Features
1. Follow the existing architecture patterns
2. Add comprehensive error handling
3. Include async/sync versions where needed
4. Update documentation and examples

### Code Style
- Follow existing naming conventions
- Add type hints for all functions
- Include docstrings with examples
- Maintain backward compatibility

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review logs for error details
3. Verify configuration files
4. Test Firebase connectivity

## 🔄 Integration with Existing System

### FastAPI Integration
The system integrates seamlessly with the existing FastAPI application:

```python
# In main.py lifespan function
async def lifespan(app: FastAPI):
    # Startup logic
    logger.info("Starting Megha Voice Agent with Dynamic Data Loading")

    # Load hospitals dynamically and warm up cache
    from voice_agent.data_loaders import CachePreloader
    preloader = CachePreloader()

    # Preload all hospitals from Firebase
    results = await preloader.preload_all_hospitals()
    logger.info(f"Preloaded {len(results)} hospitals")

    yield

    # Shutdown logic
    logger.info("Shutting down voice agent")
```

### Semantic Integration
Works with existing semantic engine:

```python
# In semantic_integration.py
async def warm_up_cache(self, hospitals_data: List[Dict[str, Any]]) -> bool:
    """Warm up semantic cache using dynamic data loading."""
    try:
        from .data_loaders import CachePreloader
        preloader = CachePreloader()

        for hospital_data in hospitals_data:
            hospital_id = hospital_data.get('id')
            if hospital_id:
                await preloader.preload_hospital_data_async(hospital_id, hospital_data)

        return True
    except Exception as e:
        logger.error(f"Error warming up cache: {e}")
        return False
```

## 🧪 Testing

### Unit Tests
Create test files for each component:

```python
# test_firebase_loader.py
import pytest
from voice_agent.data_loaders import FirebaseDataLoader

@pytest.mark.asyncio
async def test_load_hospital_data():
    loader = FirebaseDataLoader()
    data = await loader.load_hospital_data("test_hospital")
    assert "doctors" in data
    assert "tests" in data

# test_config_loader.py
def test_load_default_template():
    loader = ConfigDataLoader()
    data = loader.load_hospital_data("nonexistent_hospital")
    assert data["hospital_id"] == "nonexistent_hospital"
    assert len(data["doctors"]) > 0  # From default template
```

### Integration Tests
```python
# test_integration.py
@pytest.mark.asyncio
async def test_end_to_end_preloading():
    from voice_agent.data_loaders import CachePreloader
    from voice_agent.cache_manager import redis_manager

    preloader = CachePreloader()

    # Test preloading
    success = await preloader.preload_hospital_data_async("456")
    assert success

    # Test cache population
    stats = preloader.get_cache_statistics("456")
    assert stats["total_cached_queries"] > 0

    # Test semantic search
    results = await redis_manager.semantic_search_async(
        "डॉक्टर राजेश कब आते हैं", "456", "doctors"
    )
    assert len(results) > 0
```

## 📊 Performance Benchmarks

### Expected Performance
- **Data Loading**: < 2 seconds per hospital from Firebase
- **Query Generation**: < 500ms for 100 queries
- **Cache Population**: < 1 second for 50 queries
- **Semantic Search**: < 100ms response time

### Optimization Tips
1. **Batch Operations**: Load multiple hospitals in parallel
2. **Caching**: Use Redis for frequently accessed data
3. **Lazy Loading**: Load language templates on demand
4. **Connection Pooling**: Reuse Firebase connections

## 🔐 Security Considerations

### Firebase Security
- Use service account credentials
- Implement proper Firestore security rules
- Rotate credentials regularly
- Monitor access logs

### Configuration Security
- Store sensitive data in environment variables
- Use proper file permissions for config files
- Validate all input data
- Sanitize user-generated content

## 🌍 Internationalization

### Adding New Languages
1. **Create Language Template**:
   ```json
   // query_templates_ta.json (Tamil)
   {
     "language": "ta",
     "language_name": "Tamil",
     "doctor_name_queries": [
       "டாக்டர் {doctor_name} எப்போது வருவார்"
     ]
   }
   ```

2. **Update Hospital Configuration**:
   ```json
   {
     "languages": ["hi", "bn", "en", "ta"]
   }
   ```

3. **Add to Language Config**:
   ```python
   # In language_config.py
   SUPPORTED_LANGUAGES = ["hi", "bn", "en", "ta"]
   ```

### Language-Specific Features
- **RTL Support**: For languages like Urdu
- **Script Variations**: Devanagari, Bengali script
- **Regional Dialects**: Different query patterns per region

## 🚀 Deployment Guide

### Development Environment
```bash
# 1. Ensure Firebase credentials are set
export GOOGLE_APPLICATION_CREDENTIALS="path/to/credentials.json"

# 2. Start Redis server
redis-server

# 3. Run the voice agent
python -m voice_agent.main
```

### Production Environment
```bash
# 1. Set environment variables
export FIREBASE_CREDENTIALS="$(cat credentials.json)"
export REDIS_URL="redis://production-redis:6379"

# 2. Deploy with proper configuration
docker-compose up -d

# 3. Verify data loading
curl http://localhost:8000/health
```

### Docker Configuration
```dockerfile
# Dockerfile
FROM python:3.9-slim

# Copy configuration files
COPY voice_agent/config/ /app/voice_agent/config/

# Set environment variables
ENV PYTHONPATH=/app
ENV GOOGLE_APPLICATION_CREDENTIALS=/app/credentials.json

# Install dependencies and run
RUN pip install -r requirements.txt
CMD ["python", "-m", "voice_agent.main"]
```

## 📈 Monitoring and Alerting

### Key Metrics
- **Data Loading Success Rate**: % of successful hospital loads
- **Cache Hit Rate**: % of queries served from cache
- **Response Time**: Average semantic search response time
- **Error Rate**: % of failed operations

### Monitoring Setup
```python
# monitoring.py
import logging
from prometheus_client import Counter, Histogram, Gauge

# Metrics
data_load_counter = Counter('hospital_data_loads_total', 'Total hospital data loads')
cache_hit_rate = Gauge('cache_hit_rate', 'Cache hit rate percentage')
response_time = Histogram('semantic_search_duration_seconds', 'Semantic search response time')

# In your code
data_load_counter.inc()
response_time.observe(duration)
```

### Alerting Rules
- Alert if data loading fails for > 10% of hospitals
- Alert if cache hit rate drops below 80%
- Alert if average response time exceeds 200ms
- Alert if Redis connection fails

## 🔄 Maintenance

### Regular Tasks
1. **Update Language Templates**: Add new query patterns monthly
2. **Review Hospital Data**: Ensure Firebase data is current
3. **Monitor Performance**: Check response times and error rates
4. **Update Dependencies**: Keep libraries up to date

### Backup Strategy
- **Firebase**: Automatic backups enabled
- **Configuration Files**: Version controlled in Git
- **Redis Cache**: Can be rebuilt from source data
- **Logs**: Retained for 30 days

## 🎯 Roadmap

### Short Term (1-3 months)
- [ ] Add support for Tamil and Telugu languages
- [ ] Implement query analytics and optimization
- [ ] Add real-time data synchronization
- [ ] Performance optimization for 1000+ hospitals

### Medium Term (3-6 months)
- [ ] Machine learning for query pattern optimization
- [ ] Advanced caching strategies
- [ ] Multi-region deployment support
- [ ] Enhanced monitoring and alerting

### Long Term (6+ months)
- [ ] AI-powered query generation
- [ ] Voice pattern recognition integration
- [ ] Advanced personalization features
- [ ] Integration with hospital management systems

---

**Note**: This system completely eliminates hardcoded values and provides a production-ready, scalable solution for dynamic data loading in the voice agent system. The hybrid approach ensures reliability with Firebase priority and configuration file fallback, supporting unlimited hospitals and languages with sub-100ms performance targets.
