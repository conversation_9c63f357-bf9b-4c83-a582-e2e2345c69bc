#!/usr/bin/env node

/**
 * Database initialization script for the Hospital Staff Portal
 * Run this script to create database tables and seed initial data
*/

import { pool } from '../server/db.js';
import { logger } from '../lib/logger.js';

// Create database tables
async function createTables() {
  try {
    logger.info('Creating PostgreSQL tables...');

    // Create hospitals table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS hospitals (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        address TEXT,
        phone VARCHAR(50),
        email VARCHAR(255),
        settings JSONB DEFAULT '{}'::jsonb,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
    logger.info('Created hospitals table');

    // Create staff table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS staff (
        id VARCHAR(50) PRIMARY KEY,
        name VA<PERSON><PERSON>R(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        role VARCHAR(50) NOT NULL,
        hospital_id VARCHAR(50) NOT NULL,
        settings JSONB DEFAULT '{}'::jsonb,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        FOREIGN KEY (hospital_id) REFERENCES hospitals(id)
      )
    `);
    logger.info('Created staff table');

    // Create patients table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS patients (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        phone VARCHAR(50),
        date_of_birth DATE,
        address TEXT,
        hospital_id VARCHAR(50) NOT NULL,
        medical_history JSONB DEFAULT '{}'::jsonb,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        FOREIGN KEY (hospital_id) REFERENCES hospitals(id)
      )
    `);
    logger.info('Created patients table');

    // Create appointments table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS appointments (
        id VARCHAR(50) PRIMARY KEY,
        patient_id VARCHAR(50) NOT NULL,
        doctor_id VARCHAR(50) NOT NULL,
        hospital_id VARCHAR(50) NOT NULL,
        start_time TIMESTAMP WITH TIME ZONE NOT NULL,
        end_time TIMESTAMP WITH TIME ZONE NOT NULL,
        status VARCHAR(50) DEFAULT 'scheduled',
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        FOREIGN KEY (patient_id) REFERENCES patients(id),
        FOREIGN KEY (doctor_id) REFERENCES staff(id),
        FOREIGN KEY (hospital_id) REFERENCES hospitals(id)
      )
    `);
    logger.info('Created appointments table');

    // Create reminders table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS reminders (
        id VARCHAR(50) PRIMARY KEY,
        appointment_id VARCHAR(50) NOT NULL,
        type VARCHAR(50) NOT NULL,
        status VARCHAR(50) DEFAULT 'pending',
        scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,
        sent_time TIMESTAMP WITH TIME ZONE,
        message TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        FOREIGN KEY (appointment_id) REFERENCES appointments(id)
      )
    `);
    logger.info('Created reminders table');

    logger.info('All PostgreSQL tables created successfully');
    return true;
  } catch (error) {
    logger.error('Error creating PostgreSQL tables:', error && error.message ? error.message : error);
    // Log the full error stack for debugging
    if (error && error.stack) {
      logger.error('Stack trace:', error.stack);
    }
    throw error;
  }
}

// Create indexes for performance
async function createIndexes() {
  try {
    logger.info('Creating database indexes...');

    // Hospital indexes
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_hospitals_name ON hospitals(name);
    `);

    // Staff indexes
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_staff_hospital_id ON staff(hospital_id);
      CREATE INDEX IF NOT EXISTS idx_staff_email ON staff(email);
      CREATE INDEX IF NOT EXISTS idx_staff_role ON staff(role);
    `);

    // Patient indexes
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_patients_hospital_id ON patients(hospital_id);
      CREATE INDEX IF NOT EXISTS idx_patients_name ON patients(name);
      CREATE INDEX IF NOT EXISTS idx_patients_phone ON patients(phone);
    `);

    // Appointment indexes
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_appointments_hospital_id ON appointments(hospital_id);
      CREATE INDEX IF NOT EXISTS idx_appointments_doctor_id ON appointments(doctor_id);
      CREATE INDEX IF NOT EXISTS idx_appointments_patient_id ON appointments(patient_id);
      CREATE INDEX IF NOT EXISTS idx_appointments_start_time ON appointments(start_time);
      CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);
    `);

    // Reminder indexes
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_reminders_appointment_id ON reminders(appointment_id);
      CREATE INDEX IF NOT EXISTS idx_reminders_status ON reminders(status);
      CREATE INDEX IF NOT EXISTS idx_reminders_scheduled_time ON reminders(scheduled_time);
    `);

    logger.info('All database indexes created successfully');
    return true;
  } catch (error) {
    logger.error('Error creating database indexes:', error && error.message ? error.message : error);
    if (error && error.stack) {
      logger.error('Stack trace:', error.stack);
    }
    throw error;
  }
}

// Main function to run initialization
async function initializeDatabase() {
  try {
    logger.info('Starting database initialization...');
    
    // Create PostgreSQL tables
    await createTables();
    
    // Create indexes
    await createIndexes();
    
    logger.info('Database initialization completed successfully');
    console.log(`
====================================================
Database tables and indexes created successfully!
====================================================

Next steps:

1. Create a default hospital:
   - Use the Firebase console to create a hospital document
   - Or run: node scripts/create-hospital.js

2. Create an admin user:
   - Run: node scripts/create-admin.js --username admin --password your_secure_password --name "Admin User" --email <EMAIL> --hospital hospital_id

====================================================
    `);
    process.exit(0);
  } catch (error) {
    logger.error('Database initialization failed:', error && error.message ? error.message : error);
    if (error && error.stack) {
      logger.error('Stack trace:', error.stack);
    }
    process.exit(1);
  }
}

// Run the initialization
initializeDatabase();