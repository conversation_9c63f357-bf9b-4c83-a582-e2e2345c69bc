import json
import logging
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional
from .language_config import get_primary_language
from .cache_manager import RedisConnectionPool

# Module-local logger – configuration stays with the host app
logger = logging.getLogger(__name__)

async def _get_redis_client():
    """
    Retrieve a ready-to-use async Redis client from the global pool.
    Keeps one pool per process and lets the pool handle connection
    recycling & health-checks.

    Returns:
        Redis client instance from the centralized connection pool
    """
    pool = RedisConnectionPool()
    return pool.get_async_client()

class CallContext:
    """
    Manages and maintains the context of a voice call throughout its lifecycle.

    This class handles the persistence, retrieval, and updating of call context
    information in Redis, ensuring that context is maintained across different
    phases of a call interaction.
    """
    
    def __init__(self, call_id: Optional[str] = None, hospital_id: Optional[str] = None, caller_number: Optional[str] = None):
        """
        Initialize a new call context. Async loading/saving is handled by get_or_create.

        Args:
            call_id: Unique identifier for the call. Generated if None.
            hospital_id: Identifier for the hospital.
            caller_number: Phone number of the caller.
        """
        # Redis client will be obtained per-operation using _get_redis_client()

        self.call_id = call_id or self._generate_call_id()
        self.hospital_id = hospital_id
        self.caller_number = caller_number

        self.created_at = datetime.now().isoformat()
        self.last_updated = self.created_at
        self.state = "greeting"  # Initial state
        self.language = get_primary_language()  # Default to Hindi (primary language)
        self.entities = {}
        self.conversation_history = []
        self.state_history = [{"state": self.state, "timestamp": self.created_at}]
        self.data = {
            "patient_name": None,
            "appointment_type": None,
            "doctor_id": None,
            "test_id": None,
            "appointment_time": None,
            "emergency": False
        }
        self.dtmf_history = []
        # Note: Actual loading from cache or initial save is done in get_or_create
        logger.info(f"Initialized CallContext object for call_id: {self.call_id}")

    @staticmethod
    async def get_or_create(call_id: Optional[str] = None, hospital_id: Optional[str] = None, caller_number: Optional[str] = None) -> 'CallContext':
        """
        Factory method to get an existing call context or create a new one.
        Handles asynchronous loading from cache or saving a new context.
        """
        # If call_id is provided, use it; otherwise, a new one will be generated in __init__
        ctx = CallContext(call_id=call_id, hospital_id=hospital_id, caller_number=caller_number)

        if call_id: # Attempt to load if call_id was given
            loaded_from_cache = await ctx._load_from_cache()
            if loaded_from_cache:
                logger.info(f"Successfully loaded context for call_id: {call_id}")
                return ctx
            else:
                # Not found in cache, but call_id was provided. Treat as new context with this specific ID.
                # Populate with any provided initial details if not already set by __init__ (though __init__ now takes them)
                logger.info(f"Context for call_id: {call_id} not found in cache. Creating and saving new context with this ID.")
                if hospital_id and not ctx.hospital_id: ctx.hospital_id = hospital_id
                if caller_number and not ctx.caller_number: ctx.caller_number = caller_number
                await ctx.save() # Persist the new context
                return ctx
        else:
            # No call_id provided, so it's a brand new context (ID generated in __init__)
            # Populate with any provided initial details
            logger.info(f"No call_id provided. Creating and saving new context with generated ID: {ctx.call_id}")
            # ctx.hospital_id and ctx.caller_number are already set by __init__ if provided
            await ctx.save() # Persist the new context
            return ctx

    def _generate_call_id(self) -> str:
        """Generate a unique call ID."""
        return str(uuid.uuid4())
    
    async def _load_from_cache(self) -> bool:
        """
        Load context data from Redis cache.

        Returns:
            bool: True if context was loaded successfully, False otherwise
        """
        key = f"call:context:{self.call_id}"
        try:
            redis_client = await _get_redis_client()
            data_str = await redis_client.get(key)
            if not data_str:
                return False

            data = json.loads(data_str)
            # Update object attributes from loaded data
            # Be careful with attributes that might not exist in older cached versions
            self.created_at = data.get("created_at", self.created_at)
            self.last_updated = data.get("last_updated", self.last_updated)
            self.state = data.get("state", self.state)
            self.language = data.get("language", self.language)
            self.hospital_id = data.get("hospital_id", self.hospital_id)
            self.caller_number = data.get("caller_number", self.caller_number)
            self.state_history = data.get("state_history", self.state_history)
            self.conversation_history = data.get("conversation_history", self.conversation_history)
            self.data = data.get("data", self.data)
            self.entities = data.get("entities", self.entities)
            self.dtmf_history = data.get("dtmf_history", self.dtmf_history)
            logger.info(f"Successfully loaded and parsed context for call_id: {self.call_id}")
            return True
        except json.JSONDecodeError as e:
            logger.error(f"JSONDecodeError loading context for {self.call_id}: {e}. Data: '{data_str[:200]}...'")
            return False
        except Exception as e:
            logger.error(f"Error loading context for {self.call_id} from Redis: {e}")
            return False
            
    async def save(self, ttl: int = 600) -> bool:
        """
        Save context to Redis with TTL.
        
        Args:
            ttl: Time to live in seconds (default: 10 minutes)
            
        Returns:
            bool: Success or failure
        """
        self.last_updated = datetime.now().isoformat()
        
        data_to_save = {
            "call_id": self.call_id,
            "created_at": self.created_at,
            "last_updated": self.last_updated,
            "state": self.state,
            "language": self.language,
            "hospital_id": self.hospital_id,
            "caller_number": self.caller_number,
            "state_history": self.state_history,
            "conversation_history": self.conversation_history,
            "data": self.data,
            "entities": self.entities,
            "dtmf_history": self.dtmf_history
        }
        
        key = f"call:context:{self.call_id}"
        try:
            redis_client = await _get_redis_client()
            await redis_client.setex(key, ttl, json.dumps(data_to_save))
            logger.info(f"Successfully saved context for call_id: {self.call_id}")
            return True
        except Exception as e:
            logger.error(f"Error saving context for {self.call_id} to Redis: {e}")
            return False
    
    async def update_state(self, new_state: str, save_context: bool = True) -> bool:
        old_state = self.state
        self.state = new_state
        self.state_history.append({
            "state": new_state,
            "timestamp": datetime.now().isoformat(),
            "from_state": old_state
        })
        logger.info(f"Call {self.call_id} state changed: {old_state} -> {new_state}")
        if save_context:
            return await self.save()
        return True
    
    async def add_conversation_entry(self,
                                    speaker: str,
                                    text: str,
                                    confidence: float = None,
                                    save_context: bool = True) -> bool:
        entry = {
            "speaker": speaker,
            "text": text,
            "timestamp": datetime.now().isoformat()
        }
        if confidence is not None:
            entry["confidence"] = confidence
        self.conversation_history.append(entry)
        if save_context:
            return await self.save()
        return True
    
    async def add_dtmf_input(self, digits: str, save_context: bool = True) -> bool:
        entry = {
            "digits": digits,
            "timestamp": datetime.now().isoformat(),
            "state": self.state
        }
        self.dtmf_history.append(entry)
        if save_context:
            return await self.save()
        return True
    
    async def update_data(self, updates: Dict[str, Any], save_context: bool = True) -> bool:
        self.data.update(updates)
        if save_context:
            return await self.save()
        return True
    
    async def set_language(self, language: str, save_context: bool = True) -> bool:
        self.language = language
        if save_context:
            return await self.save()
        return True
    
    async def add_entity(self, entity_type: str, entity_value: Any, save_context: bool = True) -> bool:
        self.entities[entity_type] = entity_value
        if save_context:
            return await self.save()
        return True
    
    def get_entity(self, entity_type: str) -> Any:
        """
        Get an entity from the context.
        
        Args:
            entity_type: Type of entity to retrieve
            
        Returns:
            The entity value or None if not found
        """
        return self.entities.get(entity_type)
    
    def get_last_user_input(self) -> Optional[Dict[str, Any]]:
        """
        Get the last user input from conversation history.
        
        Returns:
            Dict with the last user input or None if not found
        """
        for entry in reversed(self.conversation_history):
            if entry["speaker"] == "user":
                return entry
        return None
    
    def get_last_system_response(self) -> Optional[Dict[str, Any]]:
        """
        Get the last system response from conversation history.
        
        Returns:
            Dict with the last system response or None if not found
        """
        for entry in reversed(self.conversation_history):
            if entry["speaker"] == "system":
                return entry
        return None
    
    def get_conversation_summary(self, max_entries: int = 5) -> str:
        """
        Get a summary of the recent conversation.
        
        Args:
            max_entries: Maximum number of conversation turns to include
            
        Returns:
            String summary of recent conversation
        """
        recent = self.conversation_history[-max_entries:] if self.conversation_history else []
        
        if not recent:
            return "No conversation history available."
            
        summary = []
        for entry in recent:
            speaker = "System" if entry["speaker"] == "system" else "User"
            summary.append(f"{speaker}: {entry['text']}")
            
        return "\n".join(summary)
    
    async def clear(self) -> bool:
        """
        Clear the call context from Redis.
        
        Returns:
            bool: Success or failure
        """
        key = f"call:context:{self.call_id}"
        try:
            redis_client = await _get_redis_client()
            await redis_client.delete(key)
            logger.info(f"Successfully cleared context for call_id: {self.call_id}")
            return True
        except Exception as e:
            logger.error(f"Error clearing context for {self.call_id} from Redis: {e}")
            return False

# The get_call_context function is effectively replaced by CallContext.get_or_create
# Users of this module should now use: await CallContext.get_or_create(...)

async def call_context_exists(call_id: str) -> bool:
    """
    Check if a call context exists in Redis.

    Args:
        call_id: Unique call identifier

    Returns:
        bool: True if context exists, False otherwise
    """
    key = f"call:context:{call_id}"

    try:
        client = await _get_redis_client()
        exists = await client.exists(key)
        return bool(exists)
    except Exception as e:
        logger.error(f"Error checking existence of context {call_id} in Redis: {e}")
        return False

async def delete_call_context(call_id: str) -> bool:
    """
    Delete a call context from Redis.

    Args:
        call_id: Unique call identifier

    Returns:
        bool: Success or failure
    """
    key = f"call:context:{call_id}"

    try:
        client = await _get_redis_client()
        deleted_count = await client.delete(key)
        logger.info(f"Deleted context for call_id: {call_id}, count: {deleted_count}")
        return deleted_count > 0
    except Exception as e:
        logger.error(f"Error deleting context {call_id} from Redis: {e}")
        return False

async def list_active_calls() -> List[str]:
    """
    List all active call IDs in the system using async Redis scan.

    Returns:
        List of active call IDs
    """
    pattern = "call:context:*"
    call_ids = []

    try:
        client = await _get_redis_client()
        async for key in client.scan_iter(match=pattern):
            call_ids.append(key.split(":")[-1])
        return call_ids
    except Exception as e:
        logger.error(f"Error listing active calls from Redis: {e}")
        return []